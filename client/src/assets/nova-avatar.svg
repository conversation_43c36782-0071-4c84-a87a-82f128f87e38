<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <!-- Fondo degradado -->
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a855f7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- C<PERSON><PERSON>ulo de fondo -->
  <circle cx="50" cy="50" r="48" fill="url(#grad1)" stroke="#ffffff" stroke-width="3" filter="url(#shadow)"/>
  
  <!-- Elementos gráficos -->
  <g id="nova-icon" transform="translate(20, 25)" filter="url(#shadow)">
    <!-- Letra N estilizada -->
    <path d="M10,5 L10,45 L20,45 L20,15 L40,45 L50,45 L50,5 L40,5 L40,35 L20,5 Z" fill="white"/>
    
    <!-- Destellos -->
    <circle cx="55" cy="10" r="6" fill="#f0f0f0" opacity="0.9"/>
    <circle cx="60" cy="5" r="3" fill="#ffffff"/>
    <path d="M5,12 L0,15 L5,18 L8,13 Z" fill="#ffffff" opacity="0.8"/>
    <path d="M55,40 L60,43 L58,48 L53,45 Z" fill="#ffffff" opacity="0.8"/>
  </g>
</svg>
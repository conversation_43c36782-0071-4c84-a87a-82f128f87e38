import React, { useEffect } from "react";
import { useAgentConversationStore } from "../../stores/agent-conversation-store";
import { ConversationView } from "./ConversationView";
import { GeneratedContent } from "./GeneratedContent";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { AlertCircle, ArrowLeft } from "lucide-react";
import { useLocation } from "wouter";

export function AgentConversationPage() {
  const [, setLocation] = useLocation();
  const {
    messages,
    agents,
    generatedContent,
    contentType,
    status,
    error,
    fetchMessages,
    fetchContent,
    reset,
  } = useAgentConversationStore();

  // Verificar si hay una sesión activa al cargar
  useEffect(() => {
    // Si hay mensajes pero no contenido final, seguir sondeando
    if (messages.length > 0 && !generatedContent && status !== "error") {
      fetchMessages();
    }

    // Si el estado es "completed" pero no tenemos contenido, obtenerlo
    if (status === "completed" && !generatedContent) {
      fetchContent();
    }
  }, [messages.length, generatedContent, status, fetchMessages, fetchContent]);

  // Manejar vuelta a la página anterior
  const handleGoBack = () => {
    // Borrar datos de la sesión actual
    reset();

    // Volver a la página principal
    setLocation("/");
  };

  // Determinar si estamos cargando
  const isLoading = status === "generating" || status === "reviewing";

  return (
    <div className="container mx-auto py-6 px-4 min-h-screen flex flex-col">
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={handleGoBack}
          className="flex items-center gap-1 mb-3"
        >
          <ArrowLeft className="h-4 w-4" />
          Volver
        </Button>

        <h1 className="text-3xl font-bold">Campañas Inteligentes</h1>
        <p className="text-gray-600">
          Observa cómo los agentes colaboran para crear tu contenido en tiempo
          real
        </p>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 flex-1 min-h-[500px]">
        {/* Vista de la conversación entre agentes */}
        <ConversationView
          messages={messages}
          agents={agents}
          isLoading={isLoading && messages.length > 0}
        />

        {/* Vista del contenido generado */}
        <GeneratedContent
          content={generatedContent}
          contentType={contentType as any}
          isLoading={isLoading && messages.length > 0}
        />
      </div>
    </div>
  );
}

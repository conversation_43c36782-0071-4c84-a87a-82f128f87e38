import { motion } from "framer-motion";
import { emma<PERSON>i<PERSON><PERSON> } from "../../assets";

interface PreloaderProps {
  isLoading: boolean;
}

export default function Preloader({ isLoading }: PreloaderProps) {
  return (
    <motion.div
      className="fixed top-0 left-0 w-full h-full bg-gradient-to-b from-[#f8f9fa] to-[#e9ecef] flex flex-col items-center justify-center z-50"
      initial={{ opacity: 1 }}
      animate={{
        opacity: isLoading ? 1 : 0,
        pointerEvents: isLoading ? "auto" : "none",
      }}
      exit={{ opacity: 0, y: -50 }}
      transition={{ duration: 0.8, ease: "easeInOut" }}
    >
      {/* Elementos decorativos de fondo */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute bg-blue-500/10 rounded-full z-0"
            style={{
              width: `${Math.random() * 120 + 40}px`,
              height: `${Math.random() * 120 + 40}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              x: [0, Math.random() * 50 - 25],
              y: [0, Math.random() * 50 - 25],
              scale: [1, 1.1, 0.9, 1],
              opacity: [0.3, 0.5, 0.3],
            }}
            transition={{
              duration: 5 + Math.random() * 3,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>
      {/* Logo con animación */}
      <motion.div
        className="mb-8 relative"
        initial={{ scale: 0, opacity: 0, rotateY: -180 }}
        animate={{ scale: 1, opacity: 1, rotateY: 0 }}
        transition={{
          delay: 0.3,
          duration: 1.2,
          type: "spring",
          stiffness: 100,
        }}
      >
        <motion.img
          src={emmaAiLogo}
          alt="Emma AI Logo"
          className="h-40 w-auto"
          animate={{
            y: [0, -10, 0],
            filter: [
              "drop-shadow(0 0 0px rgba(59, 130, 246, 0.5))",
              "drop-shadow(0 0 15px rgba(59, 130, 246, 0.8))",
              "drop-shadow(0 0 0px rgba(59, 130, 246, 0.5))",
            ],
          }}
          transition={{
            duration: 2.5,
            repeat: Infinity,
            repeatType: "loop",
            ease: "easeInOut",
          }}
        />
      </motion.div>

      {/* Barra de progreso */}
      <motion.div
        className="w-64 h-2 bg-gray-200 rounded-full overflow-hidden my-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <motion.div
          className="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-red-500"
          initial={{ width: "0%" }}
          animate={{ width: "100%" }}
          transition={{
            duration: 2.2,
            ease: "easeInOut",
          }}
        />
      </motion.div>

      {/* Texto animado */}
      <motion.p
        className="text-lg font-bold text-gray-700"
        initial={{ opacity: 0 }}
        animate={{ opacity: [0, 1, 1, 1, 0] }}
        transition={{
          delay: 0.8,
          duration: 2,
          times: [0, 0.2, 0.7, 0.8, 1],
          repeat: 1,
        }}
      >
        Preparando tu experiencia personalizada...
      </motion.p>
    </motion.div>
  );
}

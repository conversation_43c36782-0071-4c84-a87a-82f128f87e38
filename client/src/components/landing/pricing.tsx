import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence, animate } from "framer-motion";
import { cn } from "@/lib/utils";

// Counter Component - same as in PricingContainer
const Counter = ({ from, to }: { from: number; to: number }) => {
  const nodeRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    const node = nodeRef.current;
    if (!node) return;
    const controls = animate(from, to, {
      duration: 1,
      onUpdate(value) {
        node.textContent = value.toFixed(0);
      },
    });
    return () => controls.stop();
  }, [from, to]);

  return <span ref={nodeRef} />;
};

export default function Pricing() {
  const [isYearly, setIsYearly] = useState(false);

  const plans = [
    {
      name: "Básico",
      monthlyPrice: 49,
      yearlyPrice: 39, // 20% discount
      isPopular: false,
      features: [
        "3 Agentes IA Especializados",
        "5 Campañas Mensuales",
        "Acceso a Herramientas Básicas",
        "1 Estudio IA Mensual",
      ],
      accentColor: "bg-blue-500",
      textColor: "text-blue-500",
    },
    {
      name: "Profesional",
      monthlyPrice: 99,
      yearlyPrice: 79, // 20% discount
      isPopular: true,
      features: [
        "10 Agentes IA Especializados",
        "15 Campañas Mensuales",
        "Todas las Herramientas",
        "5 Estudios IA Mensuales",
        "Soporte Prioritario",
      ],
      accentColor: "bg-pink-500",
      textColor: "text-pink-500",
    },
    {
      name: "Enterprise",
      monthlyPrice: 249,
      yearlyPrice: 199, // 20% discount
      isPopular: false,
      features: [
        "Agentes IA Ilimitados",
        "Campañas Ilimitadas",
        "Herramientas Avanzadas",
        "Estudios IA Ilimitados",
        "Soporte VIP 24/7",
      ],
      accentColor: "bg-purple-500",
      textColor: "text-purple-500",
    },
  ];

  return (
    <section id="precios" className="py-20 relative">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            Planes y Precios
          </h2>
          <p className="text-xl font-bold max-w-3xl mx-auto">
            Soluciones escalables que crecen con tu negocio
          </p>
        </motion.div>

        {/* Toggle */}
        <div className="flex justify-center items-center gap-4 mb-12">
          <span
            className={`text-gray-600 font-medium ${!isYearly ? "text-black" : ""}`}
          >
            Mensual
          </span>
          <motion.button
            className="w-16 h-8 flex items-center bg-gray-200 rounded-full p-1 border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]"
            onClick={() => setIsYearly(!isYearly)}
          >
            <motion.div
              className="w-6 h-6 bg-white rounded-full border-2 border-black"
              animate={{ x: isYearly ? 32 : 0 }}
            />
          </motion.button>
          <span
            className={`text-gray-600 font-medium ${isYearly ? "text-black" : ""}`}
          >
            Anual
          </span>
          <AnimatePresence>
            {isYearly && (
              <motion.span
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="text-green-500 font-medium text-sm"
              >
                Ahorra 20%
              </motion.span>
            )}
          </AnimatePresence>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              className={cn(
                "relative bg-white rounded-xl border-3 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6 hover:shadow-[8px_8px_0px_0px_rgba(0,0,0,0.9)] transition-all duration-200",
                plan.isPopular ? "md:transform md:scale-105 md:z-10" : "",
              )}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-50px" }}
              transition={{ delay: index * 0.1, duration: 0.5 }}
              whileHover={{ y: -5 }}
            >
              {plan.isPopular && (
                <motion.div
                  className="absolute -top-6 left-0 right-0 text-center"
                  animate={{
                    y: [0, -5, 0],
                    scale: [1, 1.05, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                  }}
                >
                  <span className="inline-block px-4 py-1 bg-pink-500 text-white text-sm font-bold rounded-md border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]">
                    MÁS POPULAR
                  </span>
                </motion.div>
              )}

              {/* Price Badge */}
              <motion.div
                className={cn(
                  "absolute -top-4 -right-4 w-16 h-16 rounded-full flex items-center justify-center border-2 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,0.9)]",
                  plan.accentColor,
                )}
                animate={{
                  rotate: [0, 10, 0, -10, 0],
                  scale: [1, 1.1, 0.9, 1.1, 1],
                  y: [0, -5, 5, -3, 0],
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  ease: [0.76, 0, 0.24, 1],
                }}
              >
                <div className="text-center text-white">
                  <div className="text-lg font-black">
                    $
                    <Counter
                      from={isYearly ? plan.monthlyPrice : plan.yearlyPrice}
                      to={isYearly ? plan.yearlyPrice : plan.monthlyPrice}
                    />
                  </div>
                  <div className="text-[10px] font-bold">
                    /{isYearly ? "año" : "mes"}
                  </div>
                </div>
              </motion.div>

              <div className={cn("mb-4", plan.isPopular ? "mt-2" : "")}>
                <h3 className="text-xl font-black text-black mb-2">
                  {plan.name}
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  {index === 0
                    ? "Ideal para pequeños negocios y emprendedores"
                    : index === 1
                      ? "Perfecto para negocios en crecimiento"
                      : "Para equipos y agencias de marketing"}
                </p>
              </div>

              <div className="space-y-2 mb-6">
                {plan.features.map((feature, i) => (
                  <motion.div
                    key={feature}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: i * 0.1 }}
                    whileHover={{
                      x: 5,
                      scale: 1.02,
                      transition: { type: "spring", stiffness: 400 },
                    }}
                    className="flex items-center gap-2 p-2 bg-gray-50 rounded-md border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]"
                  >
                    <motion.span
                      whileHover={{ scale: 1.2, rotate: 360 }}
                      className={cn(
                        "w-5 h-5 rounded-md flex items-center justify-center text-white font-bold text-xs border border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,0.9)]",
                        plan.accentColor,
                      )}
                    >
                      ✓
                    </motion.span>
                    <span className="text-black font-bold text-sm">
                      {feature}
                    </span>
                  </motion.div>
                ))}
              </div>

              <motion.button
                className={cn(
                  "w-full py-2 rounded-lg text-white font-black text-sm border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] active:shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)] transition-all duration-200",
                  plan.accentColor,
                )}
                whileHover={{
                  scale: 1.02,
                  transition: { duration: 0.2 },
                }}
                whileTap={{
                  scale: 0.95,
                  rotate: [-1, 1, 0],
                }}
              >
                EMPEZAR AHORA →
              </motion.button>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}

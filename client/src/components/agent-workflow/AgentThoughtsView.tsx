import React from 'react';
import { Thought } from '../../types/unified-agent-types';

interface AgentThoughtsViewProps {
  thoughts: Record<string, Thought>;
  expandedThoughts: Record<string, boolean>;
  onThoughtExpand: (thoughtId: string) => void;
}

export const AgentThoughtsView: React.FC<AgentThoughtsViewProps> = ({
  thoughts,
  expandedThoughts,
  onThoughtExpand
}) => {
  return (
    <div className="agent-thoughts-view space-y-3">
      {Object.entries(thoughts).map(([id, thought]) => (
        <div 
          key={id} 
          className="thought-card bg-blue-50 p-3 rounded-lg cursor-pointer"
          onClick={() => onThoughtExpand(id)}
        >
          <div className="thought-header flex justify-between items-center">
            <span className="font-semibold text-sm">
              {thought.category || 'Unnamed Thought'}
            </span>
            <span className="text-xs text-gray-500">
              {new Date(thought.timestamp).toLocaleTimeString()}
            </span>
          </div>
          {expandedThoughts[id] && (
            <div className="thought-details mt-2 text-sm">
              {thought.content}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

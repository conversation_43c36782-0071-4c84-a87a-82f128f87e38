import React, { useMemo } from 'react';
import { WorkflowStep } from '../../types/unified-agent-types';
import { cn } from '../../lib/utils';

interface AgentWorkflowTimelineProps {
  steps: WorkflowStep[];
  expandedSteps: Record<string, boolean>;
  onStepExpand: (stepId: string) => void;
}

export const AgentWorkflowTimeline: React.FC<AgentWorkflowTimelineProps> = ({
  steps,
  expandedSteps,
  onStepExpand
}) => {
  const sortedSteps = useMemo(() => 
    steps.sort((a, b) => a.timestamp - b.timestamp), 
    [steps]
  );

  return (
    <div className="agent-workflow-timeline space-y-4">
      {sortedSteps.map((step) => (
        <div 
          key={step.id} 
          className={cn(
            "workflow-step p-4 rounded-lg transition-all duration-300",
            {
              "bg-blue-50": step.type === 'thinking',
              "bg-green-50": step.type === 'action',
              "bg-gray-50": step.type === 'response'
            }
          )}
        >
          <div 
            className="step-header flex justify-between items-center cursor-pointer"
            onClick={() => onStepExpand(step.id)}
          >
            <span className="text-sm font-semibold">{step.type}</span>
            <span className="text-xs text-gray-500">
              {new Date(step.timestamp).toLocaleTimeString()}
            </span>
          </div>
          {expandedSteps[step.id] && (
            <div className="step-details mt-2 text-sm">
              {step.content}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

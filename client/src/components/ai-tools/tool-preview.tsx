import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AITool } from "@/data/ai-tools-data";
import { ArrowLeft, ChevronRight } from "lucide-react";

interface ToolPreviewProps {
  tool: AITool;
  onBack: () => void;
}

export default function ToolPreview({ tool, onBack }: ToolPreviewProps) {
  const levelColors = {
    Básico: "bg-green-100 text-green-800",
    Intermedio: "bg-blue-100 text-blue-800",
    Avanzado: "bg-purple-100 text-purple-800",
  };

  return (
    <div>
      <Button
        variant="outline"
        size="sm"
        className="mb-6 flex items-center gap-1 border-2 border-black hover:bg-gray-100"
        onClick={onBack}
      >
        <ArrowLeft size={16} /> Volver a herramientas
      </Button>

      <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] overflow-hidden mb-8">
        <div className="p-8">
          <div className="flex flex-col md:flex-row md:items-center gap-4 md:gap-8 mb-6">
            <div className="flex-shrink-0 flex items-center justify-center w-16 h-16 text-4xl bg-gray-100 rounded-xl">
              {tool.icon}
            </div>
            <div>
              <h1 className="text-3xl font-black mb-2">{tool.name}</h1>
              <div className="flex items-center gap-3">
                <Badge
                  variant="outline"
                  className={`${levelColors[tool.level]}`}
                >
                  {tool.level}
                </Badge>
                <span className="text-gray-500 text-sm">
                  Categoría: {tool.category}
                </span>
              </div>
            </div>
          </div>

          <p className="text-lg mb-8">{tool.description}</p>

          <div className="mb-8">
            <h2 className="text-xl font-bold mb-4">
              Características principales
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {tool.features.map((feature, idx) => (
                <div key={idx} className="flex items-start">
                  <ChevronRight
                    size={18}
                    className="min-w-[18px] mt-0.5 mr-2 text-blue-500"
                  />
                  <span>{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      <motion.div
        className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] overflow-hidden p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <div className="text-center py-16">
          <div className="mb-4 text-6xl">{tool.icon}</div>
          <h2 className="text-2xl font-black mb-3">¡Interfaz en desarrollo!</h2>
          <p className="text-gray-600 max-w-md mx-auto mb-6">
            La interfaz interactiva de esta herramienta está actualmente en
            desarrollo. Pronto podrás utilizar todas las funcionalidades de{" "}
            {tool.name}.
          </p>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white">
            Ser notificado cuando esté disponible
          </Button>
        </div>
      </motion.div>
    </div>
  );
}

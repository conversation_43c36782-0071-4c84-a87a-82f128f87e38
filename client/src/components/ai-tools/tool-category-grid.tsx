import { motion } from "framer-motion";
import { ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { aiToolCategoriesData } from "@/data/ai-tools-data";

interface ToolCategoryGridProps {
  onCategorySelect: (categoryId: string) => void;
  onViewAll: () => void;
}

export default function ToolCategoryGrid({
  onCategorySelect,
  onViewAll,
}: ToolCategoryGridProps) {
  return (
    <div>
      <motion.div
        className="mb-6 flex justify-between items-center"
        initial={{ y: -10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <h2 className="text-2xl font-black">Categorías de Herramientas</h2>
        <Button
          variant="link"
          onClick={onViewAll}
          className="text-blue-600 hover:text-blue-800 flex items-center font-bold"
        >
          Ver todas las herramientas <ChevronRight size={16} />
        </Button>
      </motion.div>

      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {aiToolCategoriesData.map((category) => (
          <motion.div
            key={category.id}
            className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] overflow-hidden"
            whileHover={{
              y: -8,
              scale: 1.02,
              boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)",
            }}
            transition={{ type: "spring", stiffness: 300 }}
            onClick={() => onCategorySelect(category.id)}
          >
            <div
              className="h-3 w-full"
              style={{ backgroundColor: category.color }}
            ></div>
            <div className="p-6">
              <div className="flex items-center justify-between mb-3">
                <div className="text-4xl">{category.icon}</div>
                <div className="bg-gray-100 px-2 py-1 rounded-full text-sm font-bold text-gray-700">
                  {category.toolCount} Herramientas
                </div>
              </div>
              <h3 className="text-xl font-black mb-2">{category.name}</h3>
              <p className="text-gray-600 mb-4 text-sm">
                {category.description}
              </p>
              <Button className="w-full bg-white hover:bg-gray-50 text-black border-2 border-black rounded-lg shadow-[4px_4px_0px_0px_rgba(0,0,0,0.8)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.8)] hover:translate-y-[-2px] transition-all">
                Explorar Herramientas{" "}
                <ChevronRight size={16} className="ml-1" />
              </Button>
            </div>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
}

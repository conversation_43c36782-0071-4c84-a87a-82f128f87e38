"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";

export default function SimpleSEOTest() {
  const [url, setUrl] = useState("https://example.com");
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const { toast } = useToast();

  const testAnalysis = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      console.log("🚀 Starting analysis test...");
      console.log("URL:", url);

      const requestBody = {
        url: url,
        mode: "website",
        enable_progress: true,
      };

      console.log("Request body:", requestBody);

      const response = await fetch("/api/seo/analyze-website", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      console.log("Response status:", response.status);
      console.log("Response headers:", response.headers);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Response error text:", errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log("✅ Response data:", data);

      setResult(data);

      if (data.status === "started") {
        toast({
          title: "¡Análisis iniciado!",
          description: `ID: ${data.analysis_id}`,
        });

        // Test progress endpoint
        setTimeout(async () => {
          try {
            const progressResponse = await fetch(`/api/seo/progress/${data.analysis_id}`);
            const progressData = await progressResponse.json();
            console.log("📊 Progress data:", progressData);
          } catch (err) {
            console.error("Progress check failed:", err);
          }
        }, 2000);
      } else {
        toast({
          title: "Error",
          description: data.message || "Unknown error",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("❌ Analysis test failed:", error);
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      
      setResult({
        status: "error",
        message: errorMessage,
      });

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Simple SEO Analysis Test</CardTitle>
        <p className="text-sm text-muted-foreground">
          Direct test of the persistent SEO analysis system
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="url" className="text-sm font-medium">
            URL to analyze:
          </label>
          <Input
            id="url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="https://example.com"
          />
        </div>

        <Button
          onClick={testAnalysis}
          disabled={isLoading || !url}
          className="w-full"
        >
          {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
          {isLoading ? "Starting Analysis..." : "Start SEO Analysis"}
        </Button>

        {result && (
          <div className="mt-4 p-4 border rounded-lg bg-muted/50">
            <h3 className="font-medium mb-2">Result:</h3>
            <pre className="text-xs overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}

        <div className="text-xs text-muted-foreground space-y-1">
          <p>• Check browser console for detailed logs</p>
          <p>• This test bypasses the complex SEO Analyzer component</p>
          <p>• Should return an analysis_id if working correctly</p>
        </div>
      </CardContent>
    </Card>
  );
}

import React, { useState, useEffect } from "react";
import {
  DndContext,
  closestCenter,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import { SortableContext, rectSortingStrategy } from "@dnd-kit/sortable";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { PanelAgentes } from "./PanelAgentes";
import { EditorCanvas } from "./EditorCanvas";
import { ElementoDiseno } from "./ElementoDiseno";

// Definir tipos
export interface Elemento {
  id: string;
  tipo: "texto" | "imagen" | "forma";
  contenido: string;
  estilo: {
    color?: string;
    fuente?: string;
    tamano?: number;
    alineacion?: "izquierda" | "centro" | "derecha";
    x: number;
    y: number;
    ancho: number;
    alto: number;
    rotacion?: number;
  };
}

export interface Diseno {
  id: string;
  nombre: string;
  tipo:
    | "post-instagram"
    | "post-facebook"
    | "banner"
    | "tarjeta"
    | "personalizado";
  elementos: Elemento[];
  dimensiones: {
    ancho: number;
    alto: number;
  };
}

export function CanvaCreativo() {
  // Estado para el diseño actual
  const [diseno, setDiseno] = useState<Diseno>({
    id: "diseno-1",
    nombre: "Mi Diseño",
    tipo: "post-instagram",
    elementos: [],
    dimensiones: {
      ancho: 1080,
      alto: 1080,
    },
  });

  // Estado para el elemento seleccionado
  const [elementoSeleccionado, setElementoSeleccionado] = useState<
    string | null
  >(null);

  // Estado para los mensajes de agentes
  const [mensajesAgentes, setMensajesAgentes] = useState<
    { agente: string; mensaje: string; timestamp: number }[]
  >([
    {
      agente: "Nova",
      mensaje:
        "Bienvenido al editor creativo. ¿En qué tipo de diseño estás interesado?",
      timestamp: Date.now(),
    },
  ]);

  // Configurar sensores para drag and drop
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 10,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    }),
  );

  // Función para agregar un nuevo elemento al diseño
  const agregarElemento = (
    tipo: "texto" | "imagen" | "forma",
    contenido: string = "",
  ) => {
    const nuevoElemento: Elemento = {
      id: `elemento-${Date.now()}`,
      tipo,
      contenido: contenido || (tipo === "texto" ? "Nuevo Texto" : ""),
      estilo: {
        color: "#000000",
        fuente: "Inter",
        tamano: tipo === "texto" ? 24 : undefined,
        x: diseno.dimensiones.ancho / 2 - 100,
        y: diseno.dimensiones.alto / 2 - 50,
        ancho: 200,
        alto: 100,
        rotacion: 0,
      },
    };

    setDiseno({
      ...diseno,
      elementos: [...diseno.elementos, nuevoElemento],
    });

    // Añadir mensaje de agente
    setMensajesAgentes([
      ...mensajesAgentes,
      {
        agente: "Nova",
        mensaje: `He añadido un nuevo elemento de ${tipo}. ¿Quieres ajustar sus propiedades?`,
        timestamp: Date.now(),
      },
    ]);
  };

  // Función para manejar el fin de arrastre
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (active && over && active.id !== over.id) {
      // Lógica para reordenar elementos si es necesario
    }
  };

  // Función para actualizar un elemento
  const actualizarElemento = (id: string, propiedades: Partial<Elemento>) => {
    setDiseno({
      ...diseno,
      elementos: diseno.elementos.map((elemento) =>
        elemento.id === id ? { ...elemento, ...propiedades } : elemento,
      ),
    });
  };

  // Función para eliminar un elemento
  const eliminarElemento = (id: string) => {
    setDiseno({
      ...diseno,
      elementos: diseno.elementos.filter((elemento) => elemento.id !== id),
    });

    if (elementoSeleccionado === id) {
      setElementoSeleccionado(null);
    }
  };

  // Función para enviar mensaje al chat de agentes
  const enviarMensaje = (mensaje: string) => {
    // Añadir mensaje del usuario
    setMensajesAgentes([
      ...mensajesAgentes,
      { agente: "Usuario", mensaje, timestamp: Date.now() },
    ]);

    // Simular respuesta de Nova (en una versión real, esto llamaría a la API de Gemini)
    setTimeout(() => {
      setMensajesAgentes((prev) => [
        ...prev,
        {
          agente: "Nova",
          mensaje: `Entendido. Voy a ayudarte con "${mensaje.substring(0, 20)}...". ¿Alguna preferencia específica?`,
          timestamp: Date.now(),
        },
      ]);
    }, 1000);
  };

  // Cambiar dimensiones según el tipo de diseño
  useEffect(() => {
    if (diseno.tipo === "post-instagram") {
      setDiseno((prev) => ({
        ...prev,
        dimensiones: { ancho: 1080, alto: 1080 },
      }));
    } else if (diseno.tipo === "post-facebook") {
      setDiseno((prev) => ({
        ...prev,
        dimensiones: { ancho: 1200, alto: 630 },
      }));
    } else if (diseno.tipo === "banner") {
      setDiseno((prev) => ({
        ...prev,
        dimensiones: { ancho: 1500, alto: 500 },
      }));
    } else if (diseno.tipo === "tarjeta") {
      setDiseno((prev) => ({
        ...prev,
        dimensiones: { ancho: 1050, alto: 600 },
      }));
    }
  }, [diseno.tipo]);

  return (
    <div className="flex flex-col w-full p-4 space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Canva Creativo con Nova</h2>
        <div className="flex space-x-2">
          <Select
            value={diseno.tipo}
            onValueChange={(value: any) =>
              setDiseno({ ...diseno, tipo: value })
            }
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Tipo de diseño" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="post-instagram">Post Instagram</SelectItem>
              <SelectItem value="post-facebook">Post Facebook</SelectItem>
              <SelectItem value="banner">Banner</SelectItem>
              <SelectItem value="tarjeta">Tarjeta</SelectItem>
              <SelectItem value="personalizado">Personalizado</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">Guardar</Button>
          <Button>Descargar</Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 h-[calc(100vh-200px)]">
        {/* Panel de agentes e interacción - 1/5 del ancho en desktop */}
        <Card className="col-span-1 md:col-span-2 overflow-hidden">
          <CardContent className="p-0">
            <PanelAgentes
              mensajes={mensajesAgentes}
              enviarMensaje={enviarMensaje}
            />
          </CardContent>
        </Card>

        {/* Editor principal - 4/5 del ancho en desktop */}
        <Card className="col-span-1 md:col-span-3 overflow-hidden">
          <CardContent className="p-4 h-full flex flex-col">
            <Tabs defaultValue="editor" className="h-full flex flex-col">
              <TabsList>
                <TabsTrigger value="editor">Editor</TabsTrigger>
                <TabsTrigger value="elementos">Elementos</TabsTrigger>
                <TabsTrigger value="propiedades">Propiedades</TabsTrigger>
              </TabsList>

              <TabsContent value="editor" className="flex-grow overflow-auto">
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext
                    items={diseno.elementos.map((e) => e.id)}
                    strategy={rectSortingStrategy}
                  >
                    <EditorCanvas
                      diseno={diseno}
                      elementoSeleccionado={elementoSeleccionado}
                      setElementoSeleccionado={setElementoSeleccionado}
                    />
                  </SortableContext>
                </DndContext>
              </TabsContent>

              <TabsContent
                value="elementos"
                className="flex-grow overflow-auto"
              >
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 p-4">
                  <Button
                    onClick={() => agregarElemento("texto")}
                    variant="outline"
                    className="h-20 flex flex-col justify-center"
                  >
                    <span className="text-2xl">T</span>
                    <span className="text-xs mt-1">Texto</span>
                  </Button>

                  <Button
                    onClick={() => agregarElemento("imagen")}
                    variant="outline"
                    className="h-20 flex flex-col justify-center"
                  >
                    <span className="text-2xl">🖼️</span>
                    <span className="text-xs mt-1">Imagen</span>
                  </Button>

                  <Button
                    onClick={() => agregarElemento("forma")}
                    variant="outline"
                    className="h-20 flex flex-col justify-center"
                  >
                    <span className="text-2xl">◻</span>
                    <span className="text-xs mt-1">Forma</span>
                  </Button>
                </div>
              </TabsContent>

              <TabsContent
                value="propiedades"
                className="flex-grow overflow-auto"
              >
                {elementoSeleccionado ? (
                  <div className="space-y-4 p-4">
                    {diseno.elementos.find((e) => e.id === elementoSeleccionado)
                      ?.tipo === "texto" && (
                      <>
                        <div className="space-y-2">
                          <Label>Texto</Label>
                          <Input
                            value={
                              diseno.elementos.find(
                                (e) => e.id === elementoSeleccionado,
                              )?.contenido || ""
                            }
                            onChange={(e) => {
                              const elemento = diseno.elementos.find(
                                (el) => el.id === elementoSeleccionado,
                              );
                              if (elemento) {
                                actualizarElemento(elementoSeleccionado, {
                                  contenido: e.target.value,
                                });
                              }
                            }}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Color</Label>
                          <div className="flex items-center gap-2">
                            <input
                              type="color"
                              value={
                                diseno.elementos.find(
                                  (e) => e.id === elementoSeleccionado,
                                )?.estilo.color || "#000000"
                              }
                              onChange={(e) => {
                                const elemento = diseno.elementos.find(
                                  (el) => el.id === elementoSeleccionado,
                                );
                                if (elemento) {
                                  actualizarElemento(elementoSeleccionado, {
                                    estilo: {
                                      ...elemento.estilo,
                                      color: e.target.value,
                                    },
                                  });
                                }
                              }}
                              className="w-10 h-10 rounded cursor-pointer"
                            />
                            <Input
                              value={
                                diseno.elementos.find(
                                  (e) => e.id === elementoSeleccionado,
                                )?.estilo.color || "#000000"
                              }
                              onChange={(e) => {
                                const elemento = diseno.elementos.find(
                                  (el) => el.id === elementoSeleccionado,
                                );
                                if (elemento) {
                                  actualizarElemento(elementoSeleccionado, {
                                    estilo: {
                                      ...elemento.estilo,
                                      color: e.target.value,
                                    },
                                  });
                                }
                              }}
                              className="w-28"
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label>Tamaño</Label>
                          <Input
                            type="number"
                            value={
                              diseno.elementos.find(
                                (e) => e.id === elementoSeleccionado,
                              )?.estilo.tamano || 16
                            }
                            onChange={(e) => {
                              const elemento = diseno.elementos.find(
                                (el) => el.id === elementoSeleccionado,
                              );
                              if (elemento) {
                                actualizarElemento(elementoSeleccionado, {
                                  estilo: {
                                    ...elemento.estilo,
                                    tamano: parseInt(e.target.value),
                                  },
                                });
                              }
                            }}
                          />
                        </div>
                      </>
                    )}

                    <Separator />

                    <div className="space-y-2">
                      <Label>Posición</Label>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label className="text-xs">X</Label>
                          <Input
                            type="number"
                            value={
                              diseno.elementos.find(
                                (e) => e.id === elementoSeleccionado,
                              )?.estilo.x || 0
                            }
                            onChange={(e) => {
                              const elemento = diseno.elementos.find(
                                (el) => el.id === elementoSeleccionado,
                              );
                              if (elemento) {
                                actualizarElemento(elementoSeleccionado, {
                                  estilo: {
                                    ...elemento.estilo,
                                    x: parseInt(e.target.value),
                                  },
                                });
                              }
                            }}
                          />
                        </div>
                        <div>
                          <Label className="text-xs">Y</Label>
                          <Input
                            type="number"
                            value={
                              diseno.elementos.find(
                                (e) => e.id === elementoSeleccionado,
                              )?.estilo.y || 0
                            }
                            onChange={(e) => {
                              const elemento = diseno.elementos.find(
                                (el) => el.id === elementoSeleccionado,
                              );
                              if (elemento) {
                                actualizarElemento(elementoSeleccionado, {
                                  estilo: {
                                    ...elemento.estilo,
                                    y: parseInt(e.target.value),
                                  },
                                });
                              }
                            }}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Dimensiones</Label>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label className="text-xs">Ancho</Label>
                          <Input
                            type="number"
                            value={
                              diseno.elementos.find(
                                (e) => e.id === elementoSeleccionado,
                              )?.estilo.ancho || 0
                            }
                            onChange={(e) => {
                              const elemento = diseno.elementos.find(
                                (el) => el.id === elementoSeleccionado,
                              );
                              if (elemento) {
                                actualizarElemento(elementoSeleccionado, {
                                  estilo: {
                                    ...elemento.estilo,
                                    ancho: parseInt(e.target.value),
                                  },
                                });
                              }
                            }}
                          />
                        </div>
                        <div>
                          <Label className="text-xs">Alto</Label>
                          <Input
                            type="number"
                            value={
                              diseno.elementos.find(
                                (e) => e.id === elementoSeleccionado,
                              )?.estilo.alto || 0
                            }
                            onChange={(e) => {
                              const elemento = diseno.elementos.find(
                                (el) => el.id === elementoSeleccionado,
                              );
                              if (elemento) {
                                actualizarElemento(elementoSeleccionado, {
                                  estilo: {
                                    ...elemento.estilo,
                                    alto: parseInt(e.target.value),
                                  },
                                });
                              }
                            }}
                          />
                        </div>
                      </div>
                    </div>

                    <Button
                      variant="destructive"
                      onClick={() =>
                        elementoSeleccionado &&
                        eliminarElemento(elementoSeleccionado)
                      }
                    >
                      Eliminar Elemento
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-muted-foreground">
                      Selecciona un elemento para ver sus propiedades
                    </p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

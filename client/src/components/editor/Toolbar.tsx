import React from "react";
import { useEditor } from "./EditorContext";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import {
  Bold,
  Italic,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Trash2,
  Copy,
  Square,
  Circle,
  Triangle,
  Image,
  Type,
  Palette,
  BringToFront,
  SendToBack,
} from "lucide-react";

interface ToolbarProps {
  onAddImage?: () => void;
}

const Toolbar: React.FC<ToolbarProps> = ({ onAddImage }) => {
  const { selectedElementId, elements, updateElement, removeElement } =
    useEditor();

  const selectedElement = selectedElementId
    ? elements.find((el) => el.id === selectedElementId)
    : null;

  const handleFontChange = (value: string) => {
    if (selectedElementId && selectedElement?.type === "text") {
      updateElement(selectedElementId, {
        style: {
          ...selectedElement.style,
          fontFamily: value,
        },
      });
    }
  };

  const handleFontSizeChange = (value: number[]) => {
    if (selectedElementId && selectedElement?.type === "text") {
      updateElement(selectedElementId, {
        style: {
          ...selectedElement.style,
          fontSize: `${value[0]}px`,
        },
      });
    }
  };

  const toggleTextStyle = (
    style: "fontWeight" | "fontStyle",
    value: string,
  ) => {
    if (selectedElementId && selectedElement?.type === "text") {
      const currentStyle = selectedElement.style || {};
      const newStyle = {
        ...currentStyle,
        [style]: currentStyle[style] === value ? "normal" : value,
      };

      updateElement(selectedElementId, { style: newStyle });
    }
  };

  const setTextAlign = (align: "left" | "center" | "right") => {
    if (selectedElementId && selectedElement?.type === "text") {
      updateElement(selectedElementId, {
        style: {
          ...selectedElement.style,
          textAlign: align,
        },
      });
    }
  };

  const setTextColor = (color: string) => {
    if (selectedElementId && selectedElement?.type === "text") {
      updateElement(selectedElementId, {
        style: {
          ...selectedElement.style,
          color,
        },
      });
    }
  };

  const addShape = (shapeType: string) => {
    const centerX = 200;
    const centerY = 200;

    const element = {
      type: "shape" as const,
      content: shapeType,
      position: { x: centerX, y: centerY },
      style: { color: "#8b5cf6" },
    };

    return element;
  };

  const handleAddShape = (shapeType: string) => {
    const element = addShape(shapeType);
    const id = `element-${Date.now()}`;
    updateElement(id, element);
  };

  const handleAddText = () => {
    const centerX = 200;
    const centerY = 200;

    const element = {
      type: "text" as const,
      content: "Doble clic para editar texto",
      position: { x: centerX, y: centerY },
      style: {
        fontFamily: "Inter",
        fontSize: "16px",
        color: "#000000",
        textAlign: "center",
      },
    };

    return element;
  };

  const deleteElement = () => {
    if (selectedElementId) {
      removeElement(selectedElementId);
    }
  };

  return (
    <div className="toolbar p-3 space-y-4">
      {/* Controles específicos para el elemento seleccionado */}
      {selectedElement ? (
        <div className="space-y-3">
          <h3 className="text-sm font-medium">Editar elemento</h3>

          {/* Controles de texto si es un elemento de texto */}
          {selectedElement.type === "text" && (
            <>
              <div className="flex flex-wrap gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => toggleTextStyle("fontWeight", "bold")}
                  className={
                    selectedElement.style?.fontWeight === "bold"
                      ? "bg-blue-100"
                      : ""
                  }
                >
                  <Bold className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => toggleTextStyle("fontStyle", "italic")}
                  className={
                    selectedElement.style?.fontStyle === "italic"
                      ? "bg-blue-100"
                      : ""
                  }
                >
                  <Italic className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setTextAlign("left")}
                  className={
                    selectedElement.style?.textAlign === "left"
                      ? "bg-blue-100"
                      : ""
                  }
                >
                  <AlignLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setTextAlign("center")}
                  className={
                    selectedElement.style?.textAlign === "center"
                      ? "bg-blue-100"
                      : ""
                  }
                >
                  <AlignCenter className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setTextAlign("right")}
                  className={
                    selectedElement.style?.textAlign === "right"
                      ? "bg-blue-100"
                      : ""
                  }
                >
                  <AlignRight className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-2">
                <label className="text-xs font-medium">Fuente</label>
                <Select
                  defaultValue="Inter"
                  onValueChange={handleFontChange}
                  value={
                    (selectedElement.style?.fontFamily as string) || "Inter"
                  }
                >
                  <SelectTrigger className="h-8">
                    <SelectValue placeholder="Selecciona fuente" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Inter">Inter</SelectItem>
                    <SelectItem value="Arial">Arial</SelectItem>
                    <SelectItem value="Helvetica">Helvetica</SelectItem>
                    <SelectItem value="Times New Roman">
                      Times New Roman
                    </SelectItem>
                    <SelectItem value="Georgia">Georgia</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-xs font-medium">Tamaño de fuente</label>
                <Slider
                  defaultValue={[16]}
                  min={8}
                  max={72}
                  step={1}
                  onValueChange={handleFontSizeChange}
                  value={[
                    parseInt(
                      (selectedElement.style?.fontSize as string) || "16",
                    ),
                  ]}
                />
                <div className="text-xs text-right">
                  {selectedElement.style?.fontSize || "16px"}
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-xs font-medium">Color de texto</label>
                <div className="flex flex-wrap gap-2">
                  {[
                    "#000000",
                    "#ffffff",
                    "#ef4444",
                    "#3b82f6",
                    "#10b981",
                    "#f59e0b",
                    "#8b5cf6",
                  ].map((color) => (
                    <button
                      key={color}
                      className={`w-6 h-6 rounded-full ${color === "#ffffff" ? "border border-gray-300" : ""}`}
                      style={{ backgroundColor: color }}
                      onClick={() => setTextColor(color)}
                    />
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Controles para formas */}
          {selectedElement.type === "shape" && (
            <div className="space-y-3">
              <label className="text-xs font-medium">Color de forma</label>
              <div className="flex flex-wrap gap-2">
                {[
                  "#000000",
                  "#ffffff",
                  "#ef4444",
                  "#3b82f6",
                  "#10b981",
                  "#f59e0b",
                  "#8b5cf6",
                ].map((color) => (
                  <button
                    key={color}
                    className={`w-6 h-6 rounded-full ${color === "#ffffff" ? "border border-gray-300" : ""}`}
                    style={{ backgroundColor: color }}
                    onClick={() =>
                      selectedElementId &&
                      selectedElement &&
                      updateElement(selectedElementId, {
                        style: { ...selectedElement.style, color },
                      })
                    }
                  />
                ))}
              </div>
            </div>
          )}

          <div className="flex gap-2 pt-2 border-t">
            <Button variant="outline" size="sm" onClick={deleteElement}>
              <Trash2 className="h-4 w-4 mr-1" /> Eliminar
            </Button>
            <Button variant="outline" size="sm">
              <Copy className="h-4 w-4 mr-1" /> Duplicar
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-3">
          <h3 className="text-sm font-medium">Añadir elementos</h3>

          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              className="justify-start"
              onClick={() => handleAddText()}
            >
              <Type className="h-4 w-4 mr-2" /> Texto
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="justify-start"
              onClick={onAddImage}
            >
              <Image className="h-4 w-4 mr-2" /> Imagen
            </Button>
          </div>

          <div className="space-y-2">
            <label className="text-xs font-medium">Formas</label>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAddShape("square")}
              >
                <Square className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAddShape("circle")}
              >
                <Circle className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAddShape("triangle")}
              >
                <Triangle className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Toolbar;

import React, { useState, useRef, useEffect } from "react";
import html2canvas from "html2canvas";
import { ImageIcon, Type, Square, Plus } from "lucide-react";

// Componente que sustituye a Polotno debido a limitaciones de compatibilidad
interface EditorSimpleProps {
  width: number;
  height: number;
  initialText?: string;
  platform?: string;
  onPreview?: (dataUrl: string) => void;
}

export const EditorSimple: React.FC<EditorSimpleProps> = ({
  width = 1080,
  height = 1080,
  initialText = "",
  platform = "instagram",
  onPreview,
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);
  const [text, setText] = useState(initialText);
  const [color, setColor] = useState("#FFFFFF");
  const [backgroundColor, setBackgroundColor] = useState("#3498db");
  const [fontSize, setFontSize] = useState(24);
  const [image, setImage] = useState<string | null>(null);

  // Generar la vista previa usando html2canvas
  const generatePreview = () => {
    if (canvasRef.current && onPreview) {
      html2canvas(canvasRef.current).then((canvas) => {
        const dataUrl = canvas.toDataURL("image/png");
        onPreview(dataUrl);
      });
    }
  };

  // Cargar una imagen desde URL
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setImage(event.target.result as string);
        }
      };
      reader.readAsDataURL(e.target.files[0]);
    }
  };

  // Estilos
  const canvasStyle: React.CSSProperties = {
    width: `${width}px`,
    height: `${height}px`,
    backgroundColor,
    margin: "0 auto",
    position: "relative",
    overflow: "hidden",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
    fontFamily: "Arial, sans-serif",
  };

  const textStyle: React.CSSProperties = {
    color,
    fontSize: `${fontSize}px`,
    textAlign: "center",
    padding: "20px",
    maxWidth: "100%",
    wordBreak: "break-word",
    textShadow: "0 2px 4px rgba(0, 0, 0, 0.2)",
    fontWeight: 600,
    lineHeight: 1.4,
    zIndex: 2,
  };

  const imageStyle: React.CSSProperties = {
    maxWidth: "80%",
    maxHeight: "60%",
    objectFit: "contain",
    marginBottom: "20px",
    zIndex: 1,
  };

  const toolbarStyle: React.CSSProperties = {
    display: "flex",
    gap: "10px",
    marginBottom: "15px",
    flexWrap: "wrap",
    justifyContent: "center",
  };

  const toolButtonStyle: React.CSSProperties = {
    display: "flex",
    alignItems: "center",
    gap: "5px",
    padding: "6px 12px",
    border: "1px solid #ddd",
    borderRadius: "4px",
    backgroundColor: "#f8f9fa",
    cursor: "pointer",
    fontSize: "14px",
  };

  // Efectos para detectar cambios al contenido
  useEffect(() => {
    setText(initialText);
  }, [initialText]);

  return (
    <div className="editor-container p-4 flex flex-col items-center">
      {/* Barra de herramientas */}
      <div style={toolbarStyle}>
        <div style={toolButtonStyle}>
          <Type size={16} />
          <input
            type="text"
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="Editar texto"
            className="w-32 text-sm border-none bg-transparent focus:outline-none"
          />
        </div>

        <div style={toolButtonStyle}>
          <Square size={16} />
          <input
            type="color"
            value={backgroundColor}
            onChange={(e) => setBackgroundColor(e.target.value)}
            className="w-6 h-6 border-none p-0 m-0"
          />
          <span className="text-xs">Fondo</span>
        </div>

        <div style={toolButtonStyle}>
          <Type size={16} />
          <input
            type="color"
            value={color}
            onChange={(e) => setColor(e.target.value)}
            className="w-6 h-6 border-none p-0 m-0"
          />
          <span className="text-xs">Texto</span>
        </div>

        <div style={toolButtonStyle}>
          <Type size={16} />
          <input
            type="range"
            min="12"
            max="72"
            value={fontSize}
            onChange={(e) => setFontSize(parseInt(e.target.value))}
            className="w-20"
          />
          <span className="text-xs">{fontSize}px</span>
        </div>

        <label style={toolButtonStyle}>
          <ImageIcon size={16} />
          <span className="text-xs">Imagen</span>
          <input
            type="file"
            accept="image/*"
            onChange={handleImageChange}
            style={{ display: "none" }}
          />
        </label>

        <button
          style={{
            ...toolButtonStyle,
            backgroundColor: "#4285f4",
            color: "white",
            border: "none",
          }}
          onClick={generatePreview}
        >
          <Plus size={16} />
          <span>Vista Previa</span>
        </button>
      </div>

      {/* Canvas del editor */}
      <div
        ref={canvasRef}
        style={canvasStyle}
        className="canvas-container my-4"
      >
        {image && <img src={image} alt="User uploaded" style={imageStyle} />}
        <div style={textStyle}>{text || "Agrega tu texto aquí"}</div>
      </div>

      <div className="editor-info text-sm text-gray-500 mt-4 text-center">
        <p>
          Dimensiones: {width} x {height}px
        </p>
        <p>Plataforma: {platform}</p>
      </div>
    </div>
  );
};

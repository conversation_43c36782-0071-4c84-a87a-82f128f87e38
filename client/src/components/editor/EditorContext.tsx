import React, { createContext, useContext, useState, useCallback } from "react";

// Tipo para alineación de texto
export type TextAlign = "left" | "center" | "right" | "justify";

// Interfaz para el estilo de los elementos
export interface ElementStyle extends Omit<React.CSSProperties, "textAlign"> {
  textAlign?: TextAlign | string;
  color?: string;
  fontFamily?: string;
  fontSize?: string;
  fontWeight?: string;
  fontStyle?: string;
}

export interface CanvasElement {
  id: string;
  type: "text" | "image" | "shape";
  content: string;
  position: { x: number; y: number };
  style?: ElementStyle;
}

interface EditorContextType {
  elements: CanvasElement[];
  selectedElementId: string | null;
  canvasBackground: string;
  addElement: (element: Omit<CanvasElement, "id">) => void;
  updateElement: (id: string, updates: Partial<CanvasElement>) => void;
  removeElement: (id: string) => void;
  selectElement: (id: string | null) => void;
  updateElementPosition: (
    id: string,
    position: { x: number; y: number },
  ) => void;
  setBackground: (color: string) => void;
}

const EditorContext = createContext<EditorContextType | undefined>(undefined);

export const useEditor = (): EditorContextType => {
  const context = useContext(EditorContext);
  if (!context) {
    throw new Error("useEditor must be used within an EditorProvider");
  }
  return context;
};

interface EditorProviderProps {
  children: React.ReactNode;
  initialElements?: CanvasElement[];
  initialBackground?: string;
}

export const EditorProvider: React.FC<EditorProviderProps> = ({
  children,
  initialElements = [],
  initialBackground = "#ffffff",
}) => {
  const [elements, setElements] = useState<CanvasElement[]>(initialElements);
  const [selectedElementId, setSelectedElementId] = useState<string | null>(
    null,
  );
  const [canvasBackground, setCanvasBackground] =
    useState<string>(initialBackground);

  const addElement = useCallback((element: Omit<CanvasElement, "id">) => {
    const newElement: CanvasElement = {
      ...element,
      id: `element-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
    };
    setElements((prev) => [...prev, newElement]);
    return newElement.id;
  }, []);

  const updateElement = useCallback(
    (id: string, updates: Partial<CanvasElement>) => {
      setElements((prev) =>
        prev.map((element) =>
          element.id === id ? { ...element, ...updates } : element,
        ),
      );
    },
    [],
  );

  const removeElement = useCallback(
    (id: string) => {
      setElements((prev) => prev.filter((element) => element.id !== id));
      if (selectedElementId === id) {
        setSelectedElementId(null);
      }
    },
    [selectedElementId],
  );

  const selectElement = useCallback((id: string | null) => {
    setSelectedElementId(id);
  }, []);

  const updateElementPosition = useCallback(
    (id: string, position: { x: number; y: number }) => {
      updateElement(id, { position });
    },
    [updateElement],
  );

  const setBackground = useCallback((color: string) => {
    setCanvasBackground(color);
  }, []);

  const value = {
    elements,
    selectedElementId,
    canvasBackground,
    addElement,
    updateElement,
    removeElement,
    selectElement,
    updateElementPosition,
    setBackground,
  };

  return (
    <EditorContext.Provider value={value}>{children}</EditorContext.Provider>
  );
};

export default EditorContext;

import { motion } from "framer-motion";
import { Search, Filter, Grid, List, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Agent, filterAgents, categoriesData } from "@/data/agents-data";

interface AgentListingViewProps {
  categoryName: string | null;
  onAgentSelect: (agent: Agent) => void;
  searchQuery: string;
}

const AgentCard = ({
  agent,
  onSelect,
}: {
  agent: Agent;
  onSelect: (agent: Agent) => void;
}) => {
  return (
    <motion.div
      className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] overflow-hidden"
      whileHover={{
        y: -5,
        boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)",
      }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="p-6">
        <div className="flex items-center mb-4">
          <div className="w-12 h-12 mr-4 flex items-center justify-center bg-white p-1 rounded-lg border border-gray-200">
            {agent.avatar.startsWith("/") ? (
              <img
                src={agent.avatar}
                alt={agent.name}
                className="w-full h-full object-contain"
              />
            ) : (
              <div className="text-4xl">{agent.avatar}</div>
            )}
          </div>
          <div>
            <h3 className="text-xl font-black">
              {agent.name}
              {agent.isLangFlow && (
                <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  LangFlow
                </span>
              )}
            </h3>
            <div className="flex items-center mt-1">
              <span className="px-2 py-0.5 text-xs font-bold rounded-full mr-2 bg-blue-100 text-blue-800">
                {agent.level}
              </span>
              <div className="flex text-yellow-500">
                {"★".repeat(Math.floor(agent.rating))}
                {agent.rating % 1 >= 0.5 ? "½" : ""}
                {"☆".repeat(5 - Math.ceil(agent.rating))}
              </div>
            </div>
          </div>
        </div>

        <p className="text-gray-700 mb-4 text-sm">{agent.description}</p>

        <div className="mb-4">
          <h4 className="text-sm font-bold mb-2">Skills principales:</h4>
          <div className="space-y-1">
            {agent.skills.slice(0, 3).map((skill, index) => (
              <div key={index} className="flex items-center text-sm">
                <span className="text-yellow-500 mr-1">⚡</span>
                <span>{skill.name}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            className="flex-1 border-2 border-black rounded-lg shadow-[3px_3px_0px_0px_rgba(0,0,0,0.8)] hover:shadow-[5px_5px_0px_0px_rgba(0,0,0,0.8)] hover:translate-y-[-2px] transition-all"
            onClick={() => onSelect(agent)}
          >
            Ver Perfil
          </Button>
          <Button
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white border-2 border-black rounded-lg shadow-[3px_3px_0px_0px_rgba(0,0,0,0.8)] hover:shadow-[5px_5px_0px_0px_rgba(0,0,0,0.8)] hover:translate-y-[-2px] transition-all"
            onClick={() => {
              // Redireccionar según el ID del agente
              if (agent.id === "seox-analyzer") {
                window.location.href = "/seox-analyzer";
              } else if (agent.id === "instagram-copywriter") {
                window.location.href = "/instagram-copywriter";
              } else {
                // Para otros agentes, mostrar el perfil
                onSelect(agent);
              }
            }}
          >
            Probar Ahora
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

export default function AgentListingView({
  categoryName,
  onAgentSelect,
  searchQuery,
}: AgentListingViewProps) {
  // Filtrar agentes basados en la categoría y la búsqueda
  const filteredAgents = filterAgents(categoryName, searchQuery);

  // Encontrar información de la categoría seleccionada
  const selectedCategory = categoryName
    ? categoriesData.find((cat) => cat.id === categoryName)
    : null;

  return (
    <div className="flex flex-col md:flex-row gap-6">
      {/* Barra lateral de filtros */}
      <div className="w-full md:w-64 flex-shrink-0">
        <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-black">Filtros</h3>
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-gray-700"
            >
              <X size={16} className="mr-1" /> Limpiar
            </Button>
          </div>

          <div className="mb-4">
            <h4 className="flex w-full items-center justify-between py-2 font-bold text-left">
              Nivel
            </h4>
            <div className="pt-2 space-y-2">
              {["Básico", "Intermedio", "Avanzado", "Experto"].map((level) => (
                <div key={level} className="flex items-center space-x-2">
                  <div className="h-5 w-5 rounded border-2 border-black"></div>
                  <label className="text-sm font-medium leading-none">
                    {level}
                  </label>
                </div>
              ))}
            </div>
          </div>

          <div className="mb-4">
            <h4 className="flex w-full items-center justify-between py-2 font-bold text-left">
              Skills
            </h4>
            <div className="pt-2 space-y-2">
              {["Copywriting", "SEO", "Redes Sociales", "Análisis"].map(
                (skill) => (
                  <div key={skill} className="flex items-center space-x-2">
                    <div className="h-5 w-5 rounded border-2 border-black"></div>
                    <label className="text-sm font-medium leading-none">
                      {skill}
                    </label>
                  </div>
                ),
              )}
            </div>
          </div>

          <div className="mb-4">
            <h4 className="flex w-full items-center justify-between py-2 font-bold text-left">
              Rating
            </h4>
            <div className="pt-2 space-y-2">
              {["5 estrellas", "4+ estrellas", "3+ estrellas"].map((rating) => (
                <div key={rating} className="flex items-center space-x-2">
                  <div className="h-5 w-5 rounded border-2 border-black"></div>
                  <label className="text-sm font-medium leading-none">
                    {rating}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Área principal de contenido */}
      <div className="flex-grow">
        <motion.div
          initial={{ y: -10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6 mb-6">
            <h2 className="text-2xl font-black mb-2">
              {selectedCategory ? selectedCategory.name : "Todos los Agentes"}
            </h2>
            <p className="text-gray-600">
              {selectedCategory
                ? selectedCategory.description
                : "Explora todos los agentes IA disponibles en el marketplace"}
            </p>
          </div>

          {/* Barra de herramientas */}
          <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-4 mb-6 flex flex-wrap justify-between items-center gap-4">
            <div className="flex items-center">
              <div className="flex items-center">
                <span className="text-sm font-medium mr-2">Ordenar por:</span>
                <select className="border-2 border-black rounded-lg shadow-[2px_2px_0px_0px_rgba(0,0,0,0.8)] py-1 px-2 text-sm focus:outline-none">
                  <option value="relevant">Relevancia</option>
                  <option value="rating">Rating</option>
                  <option value="az">A-Z</option>
                  <option value="za">Z-A</option>
                </select>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">
                {filteredAgents.length} Agentes encontrados
              </span>
              <div className="flex rounded-lg border-2 border-black overflow-hidden shadow-[3px_3px_0px_0px_rgba(0,0,0,0.9)]">
                <Button className="px-2 py-1 rounded-none border-0 bg-blue-600 text-white">
                  <Grid size={18} />
                </Button>
                <Button className="px-2 py-1 rounded-none border-0 bg-white text-gray-700">
                  <List size={18} />
                </Button>
              </div>
            </div>
          </div>

          {/* Grid de Agentes */}
          <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {filteredAgents.map((agent) => (
              <AgentCard
                key={agent.id}
                agent={agent}
                onSelect={onAgentSelect}
              />
            ))}

            {filteredAgents.length === 0 && (
              <div className="col-span-full py-20 text-center">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-2xl font-bold mb-2">
                  No se encontraron agentes
                </h3>
                <p className="text-gray-600">
                  No hemos encontrado agentes que coincidan con tu búsqueda.
                  Intenta con otros términos o categorías.
                </p>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
}

#!/usr/bin/env python3
"""
Poetry CI/CD Integration Generator

This script generates CI/CD configuration files for Poetry-based Python projects
across various platforms (GitHub Actions, GitLab CI, Jenkins, CircleCI, AWS).
"""

import sys
import os
from pathlib import Path

# ANSI colors for prettier output
GREEN = "\033[0;32m"
YELLOW = "\033[1;33m"
RED = "\033[0;31m"
BLUE = "\033[0;34m"
NC = "\033[0m"  # No Color

# GitHub Actions workflow
GITHUB_WORKFLOW = """name: Python Poetry CI

on:
  push:
    branches: [ main, master, develop ]
    paths:
      - 'backend/**'
  pull_request:
    branches: [ main, master, develop ]
    paths:
      - 'backend/**'

jobs:
  test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'

    - name: Install Poetry
      run: |
        curl -sSL https://install.python-poetry.org | python3 -
        echo "$HOME/.local/bin" >> $GITHUB_PATH

    - name: Configure Poetry
      run: |
        poetry config virtualenvs.in-project true

    - name: Cache Poetry dependencies
      uses: actions/cache@v3
      with:
        path: ./backend/.venv
        key: ${{ runner.os }}-poetry-${{ hashFiles('**/poetry.lock') }}
        restore-keys: |
          ${{ runner.os }}-poetry-

    - name: Install dependencies
      run: poetry install

    - name: Lint with flake8
      run: |
        poetry run flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        poetry run flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    - name: Type check with mypy
      run: poetry run mypy .

    - name: Test with pytest
      run: poetry run pytest

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push'

    steps:
    - uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_HUB_USERNAME }}
        password: ${{ secrets.DOCKER_HUB_TOKEN }}

    - name: Build and push
      uses: docker/build-push-action@v4
      with:
        context: ./backend
        file: ./backend/Dockerfile.poetry
        push: true
        tags: yourusername/plataforma-backend:latest
"""

# GitLab CI configuration
GITLAB_CI = """# Poetry-based GitLab CI configuration
image: python:3.11-slim

variables:
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.pip-cache"
  POETRY_CACHE_DIR: "$CI_PROJECT_DIR/.poetry-cache"

# Cache dependencies between jobs
cache:
  key: 
    files:
      - poetry.lock
  paths:
    - .pip-cache/
    - .poetry-cache/
    - .venv/

stages:
  - lint
  - test
  - build
  - deploy

before_script:
  - cd backend
  - apt-get update && apt-get install -y curl
  - curl -sSL https://install.python-poetry.org | python3 -
  - export PATH="/root/.local/bin:$PATH"
  - poetry config virtualenvs.in-project true
  - poetry install

lint:
  stage: lint
  script:
    - poetry run flake8
    - poetry run black --check .
    - poetry run mypy .

test:
  stage: test
  script:
    - poetry run pytest --cov=app
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml

build:
  stage: build
  image: docker:20.10
  services:
    - docker:20.10-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  script:
    - cd backend
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG -f Dockerfile.poetry .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG
  only:
    - main
    - master
    - develop

deploy:
  stage: deploy
  image: alpine:latest
  script:
    - echo "Deploying application..."
    # Add your deployment commands here
  environment:
    name: production
  only:
    - main
    - master
"""

# Jenkins Pipeline
JENKINS_PIPELINE = """pipeline {
    agent {
        docker {
            image 'python:3.11-slim'
            args '-v $HOME/.cache:/root/.cache'
        }
    }
    
    environment {
        POETRY_HOME = "$HOME/.poetry"
        PATH = "$POETRY_HOME/bin:$PATH"
    }
    
    stages {
        stage('Setup') {
            steps {
                dir('backend') {
                    sh 'apt-get update && apt-get install -y curl'
                    sh 'curl -sSL https://install.python-poetry.org | python3 -'
                    sh 'poetry config virtualenvs.in-project true'
                    sh 'poetry install'
                }
            }
        }
        
        stage('Lint') {
            steps {
                dir('backend') {
                    sh 'poetry run flake8'
                    sh 'poetry run black --check .'
                    sh 'poetry run mypy .'
                }
            }
        }
        
        stage('Test') {
            steps {
                dir('backend') {
                    sh 'poetry run pytest --junitxml=test-reports/junit.xml --cov=app --cov-report=xml'
                }
            }
            post {
                always {
                    junit 'backend/test-reports/junit.xml'
                    cobertura coberturaReportFile: 'backend/coverage.xml'
                }
            }
        }
        
        stage('Build') {
            when {
                anyOf {
                    branch 'main'
                    branch 'master'
                    branch 'develop'
                }
            }
            agent {
                docker {
                    image 'docker:20.10'
                    args '-v /var/run/docker.sock:/var/run/docker.sock'
                }
            }
            steps {
                dir('backend') {
                    sh 'docker build -t plataforma-backend:$GIT_COMMIT -f Dockerfile.poetry .'
                }
            }
        }
        
        stage('Deploy') {
            when {
                anyOf {
                    branch 'main'
                    branch 'master'
                }
            }
            steps {
                echo 'Deploying to production...'
                // Add deployment steps here
            }
        }
    }
    
    post {
        always {
            cleanWs()
        }
    }
}
"""

# CircleCI config
CIRCLECI_CONFIG = """version: 2.1

orbs:
  python: circleci/python@2.1.1
  docker: circleci/docker@2.2.0

jobs:
  test:
    docker:
      - image: cimg/python:3.11
    steps:
      - checkout
      - python/install-packages:
          pkg-manager: pip
          packages:
            - poetry
      - restore_cache:
          keys:
            - poetry-deps-{{ checksum "backend/poetry.lock" }}
      - run:
          name: Install dependencies
          command: |
            cd backend
            poetry config virtualenvs.in-project true
            poetry install
      - save_cache:
          key: poetry-deps-{{ checksum "backend/poetry.lock" }}
          paths:
            - backend/.venv
      - run:
          name: Run linting
          command: |
            cd backend
            poetry run flake8
            poetry run black --check .
            poetry run mypy .
      - run:
          name: Run tests
          command: |
            cd backend
            poetry run pytest --junitxml=test-reports/junit.xml --cov=app --cov-report=xml
      - store_test_results:
          path: backend/test-reports
      - store_artifacts:
          path: backend/test-reports

  build:
    docker:
      - image: cimg/python:3.11
    steps:
      - checkout
      - setup_remote_docker:
          version: 20.10.14
      - docker/check
      - docker/build:
          path: backend
          dockerfile: Dockerfile.poetry
          image: plataforma-backend
          tag: ${CIRCLE_SHA1}
      - docker/push:
          image: plataforma-backend
          tag: ${CIRCLE_SHA1}
          
workflows:
  version: 2
  test-build-deploy:
    jobs:
      - test
      - build:
          requires:
            - test
          filters:
            branches:
              only:
                - main
                - master
                - develop
"""

# AWS CodeBuild buildspec.yml
AWS_BUILDSPEC = """version: 0.2

phases:
  install:
    runtime-versions:
      python: 3.11
    commands:
      - curl -sSL https://install.python-poetry.org | python3 -
      - export PATH="/root/.local/bin:$PATH"
      - cd backend
      - poetry config virtualenvs.in-project true

  pre_build:
    commands:
      - cd backend
      - poetry install
      - poetry run flake8
      - poetry run black --check .
      - poetry run mypy .
      - poetry run pytest --cov=app

  build:
    commands:
      - cd backend
      - echo "Building Docker image..."
      - docker build -t $ECR_REPOSITORY_URI:$CODEBUILD_RESOLVED_SOURCE_VERSION -f Dockerfile.poetry .
      - echo "Logging in to Amazon ECR..."
      - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REPOSITORY_URI
      - echo "Pushing Docker image..."
      - docker push $ECR_REPOSITORY_URI:$CODEBUILD_RESOLVED_SOURCE_VERSION
      - docker tag $ECR_REPOSITORY_URI:$CODEBUILD_RESOLVED_SOURCE_VERSION $ECR_REPOSITORY_URI:latest
      - docker push $ECR_REPOSITORY_URI:latest

  post_build:
    commands:
      - cd backend
      - echo "Updating ECS service..."
      - aws ecs update-service --cluster $ECS_CLUSTER --service $ECS_SERVICE --force-new-deployment

artifacts:
  files:
    - backend/poetry.lock
    - backend/pyproject.toml
    - backend/Dockerfile.poetry
    - appspec.yml
  discard-paths: no

cache:
  paths:
    - /root/.cache/pip/**/*
    - /root/.cache/poetry/**/*
    - backend/.venv/**/*
"""

def create_directory_if_not_exists(directory):
    """Create a directory if it doesn't exist."""
    Path(directory).mkdir(parents=True, exist_ok=True)

def write_file(file_path, content):
    """Write content to a file."""
    try:
        with open(file_path, 'w') as f:
            f.write(content)
        print(f"{GREEN}Created {file_path}{NC}")
        return True
    except Exception as e:
        print(f"{RED}Error creating {file_path}: {str(e)}{NC}")
        return False

def generate_github_workflow():
    """Generate GitHub Actions workflow file."""
    workflow_dir = ".github/workflows"
    create_directory_if_not_exists(workflow_dir)
    return write_file(f"{workflow_dir}/python-poetry-ci.yml", GITHUB_WORKFLOW)

def generate_gitlab_ci():
    """Generate GitLab CI configuration file."""
    return write_file(".gitlab-ci.yml", GITLAB_CI)

def generate_jenkins_pipeline():
    """Generate Jenkinsfile for Jenkins Pipeline."""
    return write_file("Jenkinsfile", JENKINS_PIPELINE)

def generate_circle_ci():
    """Generate CircleCI configuration."""
    circle_dir = ".circleci"
    create_directory_if_not_exists(circle_dir)
    return write_file(f"{circle_dir}/config.yml", CIRCLECI_CONFIG)

def generate_aws_buildspec():
    """Generate AWS CodeBuild buildspec.yml."""
    return write_file("buildspec.yml", AWS_BUILDSPEC)

def main():
    print(f"{BLUE}Poetry CI/CD Integration Generator{NC}")
    print(f"{BLUE}================================{NC}")
    
    if len(sys.argv) < 2:
        print(f"{YELLOW}Please specify a CI/CD platform:{NC}")
        print(f"  github   - GitHub Actions")
        print(f"  gitlab   - GitLab CI")
        print(f"  jenkins  - Jenkins Pipeline")
        print(f"  circle   - CircleCI")
        print(f"  aws      - AWS CodeBuild")
        print(f"  all      - Generate all configurations")
        print(f"\nUsage: {sys.argv[0]} <platform>")
        return
    
    platform = sys.argv[1].lower()
    
    if platform == "github":
        generate_github_workflow()
    elif platform == "gitlab":
        generate_gitlab_ci()
    elif platform == "jenkins":
        generate_jenkins_pipeline()
    elif platform == "circle":
        generate_circle_ci()
    elif platform == "aws":
        generate_aws_buildspec()
    elif platform == "all":
        print(f"{BLUE}Generating all CI/CD configurations...{NC}")
        generate_github_workflow()
        generate_gitlab_ci()
        generate_jenkins_pipeline()
        generate_circle_ci()
        generate_aws_buildspec()
    else:
        print(f"{RED}Unknown platform: {platform}{NC}")
        return
    
    print(f"\n{GREEN}CI/CD configuration(s) generated successfully!{NC}")
    print(f"{YELLOW}You may need to customize the generated files for your specific needs.{NC}")

if __name__ == "__main__":
    main()
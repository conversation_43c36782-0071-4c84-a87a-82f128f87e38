#!/usr/bin/env python3
"""
Test script for persistent SEO analysis system
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from app.services.persistent_seo_service import persistent_seo_service
from app.db.session import get_db
from app.db.models import SEOAnalysis

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_database_connection():
    """Test database connection and table existence."""
    logger.info("🔍 Testing database connection...")
    
    try:
        db = next(get_db())
        
        # Test basic query
        count = db.query(SEOAnalysis).count()
        logger.info(f"✅ Database connection successful. Found {count} existing analyses.")
        
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Database connection failed: {str(e)}")
        return False


async def test_persistent_service():
    """Test the persistent SEO service."""
    logger.info("🧪 Testing persistent SEO service...")
    
    try:
        # Test starting an analysis
        result = await persistent_seo_service.start_analysis(
            url="https://example.com",
            mode="page",
            user_id="test_user",
            enable_progress=True
        )
        
        if result.get("status") == "started":
            analysis_id = result.get("analysis_id")
            logger.info(f"✅ Analysis started successfully: {analysis_id}")
            
            # Test getting progress
            progress = persistent_seo_service.get_analysis_progress(analysis_id)
            if progress:
                logger.info(f"✅ Progress retrieved successfully: {progress.get('status')}")
            else:
                logger.error("❌ Failed to retrieve progress")
                return False
            
            # Test cancelling analysis
            cancelled = await persistent_seo_service.cancel_analysis(analysis_id)
            if cancelled:
                logger.info("✅ Analysis cancelled successfully")
            else:
                logger.error("❌ Failed to cancel analysis")
                return False
            
            return True
        else:
            logger.error(f"❌ Failed to start analysis: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Persistent service test failed: {str(e)}")
        logger.exception(e)
        return False


async def test_user_analyses():
    """Test getting user analyses."""
    logger.info("📋 Testing user analyses retrieval...")
    
    try:
        analyses = persistent_seo_service.get_user_analyses(
            user_id="test_user",
            limit=10
        )
        
        logger.info(f"✅ Retrieved {len(analyses)} analyses for test user")
        return True
        
    except Exception as e:
        logger.error(f"❌ User analyses test failed: {str(e)}")
        return False


async def cleanup_test_data():
    """Clean up test data."""
    logger.info("🧹 Cleaning up test data...")
    
    try:
        db = next(get_db())
        
        # Delete test analyses
        deleted = db.query(SEOAnalysis).filter(
            SEOAnalysis.user_id == "test_user"
        ).delete()
        
        db.commit()
        db.close()
        
        logger.info(f"✅ Cleaned up {deleted} test analyses")
        return True
        
    except Exception as e:
        logger.error(f"❌ Cleanup failed: {str(e)}")
        return False


async def main():
    """Main test function."""
    logger.info("🚀 Starting Persistent SEO Analysis System Tests")
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Persistent Service", test_persistent_service),
        ("User Analyses", test_user_analyses),
        ("Cleanup", cleanup_test_data),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running test: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            if result:
                logger.info(f"✅ {test_name} PASSED")
                passed += 1
            else:
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED with exception: {str(e)}")
            logger.exception(e)
    
    logger.info(f"\n{'='*50}")
    logger.info(f"TEST RESULTS: {passed}/{total} tests passed")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 All tests passed! Persistent SEO system is working correctly.")
        return True
    else:
        logger.error(f"❌ {total - passed} tests failed. Please check the logs above.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

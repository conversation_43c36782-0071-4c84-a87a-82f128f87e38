# Poetry-based Development Dockerfile for Plataforma Backend
# Optimized for development workflow with hot-reloading

FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install Poetry and necessary build deps
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        gcc \
        libpq-dev \
        curl \
        build-essential \
        git \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && pip install --no-cache-dir poetry==2.1.2

# Configure Poetry - Don't create virtual environment in container
RUN poetry config virtualenvs.create false

# Copy Poetry files
COPY pyproject.toml poetry.lock* ./

# Install all dependencies including development ones
RUN poetry install --no-interaction

# We don't copy application code here since we'll mount it as a volume
# in the docker-compose file for hot reloading

# Set environment variables
ENV PORT=8000
ENV HOST=0.0.0.0
ENV PYTHONPATH=/app:$PYTHONPATH
ENV PYTHONUNBUFFERED=1
ENV DEBUG=true

# Expose the port
EXPOSE 8000

# Health check that tolerates startup time in development mode
HEALTHCHECK --interval=30s --timeout=30s --start-period=30s --retries=5 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start app with reload
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
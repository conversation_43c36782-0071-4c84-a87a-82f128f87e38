# Simplified CrewAI Integration Guide

This guide provides a straightforward approach to using CrewAI in the application, focusing on simplicity and reliability.

## Basic CrewAI Concepts

1. **Agent**: An AI agent with a specific role, goal, and backstory.
2. **Task**: A task that an agent needs to perform.
3. **Crew**: A team of agents working together on tasks.
4. **Process**: How tasks are executed (sequential or hierarchical).

## Simple Implementation

### 1. Basic Agent Creation

```python
from crewai import Agent

agent = Agent(
    role="Content Writer",
    goal="Write engaging content on various topics",
    backstory="You are an experienced content writer with expertise in creating engaging articles.",
    verbose=True
)
```

### 2. Using Google Gemini

```python
from langchain_google_genai import ChatGoogleGenerativeAI

# Create Google Gemini LLM
gemini_llm = ChatGoogleGenerativeAI(
    model="gemini-pro",
    temperature=0.7
)

# Create agent with Gemini
agent = Agent(
    role="Content Writer",
    goal="Write engaging content",
    backstory="You are an experienced content writer.",
    llm=gemini_llm,  # Use Gemini LLM
    verbose=True
)
```

### 3. Creating Tasks

```python
from crewai import Task

task = Task(
    description="Write a short paragraph about artificial intelligence.",
    expected_output="A well-written paragraph about AI.",
    agent=agent
)
```

### 4. Creating a Crew

```python
from crewai import Crew, Process

crew = Crew(
    agents=[agent],
    tasks=[task],
    verbose=True,
    process=Process.sequential
)

# Run the crew
result = crew.kickoff()
```

### 5. Using Tools

```python
from langchain.tools import tool

@tool
def search_web(query: str) -> str:
    """Search the web for information."""
    # Implementation here
    return f"Search results for '{query}'"

# Create agent with tool
agent = Agent(
    role="Researcher",
    goal="Find information",
    backstory="You are a skilled researcher.",
    tools=[search_web],
    verbose=True
)
```

## Working Examples

The following working examples are provided in the `backend` directory:

1. `simple_working_crew.py`: Basic example with default OpenAI.
2. `simple_working_crew_gemini.py`: Example with Google Gemini.
3. `simple_working_crew_with_tools.py`: Example with multiple agents and tools.

## Integration with the Application

### Agent Factory

```python
# In agent_factory.py
from crewai import Agent

def create_agent(config):
    """Create an agent from configuration."""
    return Agent(
        role=config["role"],
        goal=config["goal"],
        backstory=config["backstory"],
        llm=get_llm(config["llm_provider"]),
        verbose=True
    )

def get_llm(provider):
    """Get LLM based on provider."""
    if provider == "gemini":
        from langchain_google_genai import ChatGoogleGenerativeAI
        return ChatGoogleGenerativeAI(
            model="gemini-pro",
            temperature=0.7
        )
    # Add other providers as needed
    return None
```

### Crew Manager

```python
# In crew_manager.py
from crewai import Crew, Process

def create_crew(agents, tasks):
    """Create a crew from agents and tasks."""
    return Crew(
        agents=agents,
        tasks=tasks,
        verbose=True,
        process=Process.sequential
    )

def run_crew(crew):
    """Run a crew and return the result."""
    try:
        return crew.kickoff()
    except Exception as e:
        print(f"Error running crew: {str(e)}")
        return None
```

## Best Practices

1. **Keep It Simple**: Start with the simplest implementation that works.
2. **Use Direct Approach**: Avoid unnecessary abstractions and complexity.
3. **Test Incrementally**: Test each component separately before integrating.
4. **Handle Errors**: Implement proper error handling to catch and report issues.
5. **Use Environment Variables**: Store API keys in environment variables.

## Troubleshooting

1. **API Key Issues**: Ensure the correct API key is set in the environment.
2. **Dependency Conflicts**: Use the `fix_crewai_dependencies.py` script to fix dependency issues.
3. **Parameter Errors**: Ensure you're using the correct parameters for CrewAI 0.11.2.
4. **Memory Issues**: If using shared memory, keep the implementation simple.

## Conclusion

By following this simplified guide, you can integrate CrewAI into the application with minimal complexity and maximum reliability.

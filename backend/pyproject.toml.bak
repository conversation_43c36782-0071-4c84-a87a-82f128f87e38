[tool.poetry]
name = "emma-studio"
version = "0.1.0"
description = "Backend for Emma Studio"
authors = ["Emma Studio Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = ">=3.10,<3.13"  # Specify Python version compatibility
setuptools = "^80.8.0"
fastapi = "0.104.1"  # Downgrade to a version compatible with Python 3.12
uvicorn = {extras = ["standard"], version = "0.24.0"}
pydantic = "2.5.3"  # Use a version compatible with FastAPI 0.104.1
pydantic-settings = "2.1.0"
python-dotenv = "^1.0.0"
requests = "^2.31.0"
openai = "^1.0.0"
redis = "^5.0.1"
google-generativeai = "^0.3.0"
loguru = "^0.7.3"
sentry-sdk = {extras = ["fastapi"], version = "^2.27.0"}
sqlalchemy = "^2.0.0"
alembic = "^1.15.0"
httpx = "^0.27.0"
typing-extensions = "^4.10.0"
starlette = "0.27.0"  # Compatible with FastAPI 0.104.1
python-multipart = "^0.0.6"

# Note: Emma Studio uses a custom agent implementation, not CrewAI
# The following dependencies are for AI services and utilities
openai = "^1.0.0"
google-generativeai = "^0.3.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
black = "^23.11.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"
pre-commit = "^4.2.0"
isort = "^5.12.0"
pytest-asyncio = "^0.23.5"
pytest-cov = "^4.1.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

# Add tool configurations for better development experience
[tool.black]
line-length = 88
target-version = ["py310", "py311", "py312"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false
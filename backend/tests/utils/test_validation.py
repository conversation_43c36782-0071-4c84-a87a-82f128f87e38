"""
Tests for the validation utilities.
"""

import pytest
from app.utils.validation import (
    sanitize_string,
    validate_string,
    validate_dict,
    validate_list
)


class TestSanitizeString:
    """Tests for the sanitize_string function."""
    
    def test_sanitize_html(self):
        """Test that HTML is properly escaped."""
        input_str = "<script>alert('XSS')</script>"
        expected = "&lt;script&gt;alert('XSS')&lt;/script&gt;"
        assert sanitize_string(input_str) == expected
    
    def test_sanitize_control_chars(self):
        """Test that control characters are removed."""
        input_str = "Hello\x00World\x1F"
        expected = "HelloWorld"
        assert sanitize_string(input_str) == expected
    
    def test_sanitize_empty_string(self):
        """Test that empty strings are handled correctly."""
        assert sanitize_string("") == ""
        assert sanitize_string(None) is None


class TestValidateString:
    """Tests for the validate_string function."""
    
    def test_validate_string_success(self):
        """Test successful string validation."""
        input_str = "Hello, World!"
        assert validate_string(input_str) == input_str
    
    def test_validate_string_min_length(self):
        """Test minimum length validation."""
        with pytest.raises(ValueError, match="String too short"):
            validate_string("Hi", min_length=3)
    
    def test_validate_string_max_length(self):
        """Test maximum length validation."""
        with pytest.raises(ValueError, match="String too long"):
            validate_string("Hello, World!", max_length=5)
    
    def test_validate_string_pattern(self):
        """Test pattern validation."""
        # Valid email pattern
        assert validate_string(
            "<EMAIL>",
            pattern=r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        ) == "<EMAIL>"
        
        # Invalid email
        with pytest.raises(ValueError, match="does not match required pattern"):
            validate_string(
                "not-an-email",
                pattern=r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
            )
    
    def test_validate_string_type(self):
        """Test type validation."""
        with pytest.raises(ValueError, match="Expected string"):
            validate_string(123)


class TestValidateDict:
    """Tests for the validate_dict function."""
    
    def test_validate_dict_success(self):
        """Test successful dictionary validation."""
        input_dict = {"name": "John", "age": 30}
        assert validate_dict(
            input_dict,
            required_keys=["name"],
            optional_keys=["age"]
        ) == input_dict
    
    def test_validate_dict_missing_required(self):
        """Test validation with missing required keys."""
        with pytest.raises(ValueError, match="Missing required key"):
            validate_dict(
                {"age": 30},
                required_keys=["name"]
            )
    
    def test_validate_dict_unexpected_keys(self):
        """Test validation with unexpected keys."""
        with pytest.raises(ValueError, match="Unexpected key"):
            validate_dict(
                {"name": "John", "age": 30, "extra": "value"},
                required_keys=["name"],
                optional_keys=["age"],
                allow_extra_keys=False
            )
    
    def test_validate_dict_with_validators(self):
        """Test validation with custom validators."""
        def validate_age(age):
            if not isinstance(age, int) or age < 0 or age > 120:
                raise ValueError("Age must be between 0 and 120")
            return age
        
        # Valid age
        assert validate_dict(
            {"name": "John", "age": 30},
            validators={"age": validate_age}
        ) == {"name": "John", "age": 30}
        
        # Invalid age
        with pytest.raises(ValueError, match="Invalid value for age"):
            validate_dict(
                {"name": "John", "age": 150},
                validators={"age": validate_age}
            )


class TestValidateList:
    """Tests for the validate_list function."""
    
    def test_validate_list_success(self):
        """Test successful list validation."""
        input_list = [1, 2, 3]
        assert validate_list(input_list) == input_list
    
    def test_validate_list_min_length(self):
        """Test minimum length validation."""
        with pytest.raises(ValueError, match="List too short"):
            validate_list([1], min_length=2)
    
    def test_validate_list_max_length(self):
        """Test maximum length validation."""
        with pytest.raises(ValueError, match="List too long"):
            validate_list([1, 2, 3], max_length=2)
    
    def test_validate_list_with_item_validator(self):
        """Test validation with item validator."""
        def validate_positive(num):
            if not isinstance(num, int) or num <= 0:
                raise ValueError("Number must be positive")
            return num
        
        # Valid items
        assert validate_list(
            [1, 2, 3],
            item_validator=validate_positive
        ) == [1, 2, 3]
        
        # Invalid item
        with pytest.raises(ValueError, match="Invalid item at index 1"):
            validate_list(
                [1, -2, 3],
                item_validator=validate_positive
            )

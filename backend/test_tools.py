"""
Test script for the agent tools system.
"""
import os
import asyncio
import logging
from dotenv import load_dotenv

from agents.tools import default_tool_factory, register_default_tools
from agents.tools.llm.gemini_tool import GeminiTool
from agents.tools.content.content_analyzer_tool import ContentAnalyzerTool
from agents.tools.content.seo_analyzer_tool import SEOAnalyzerTool

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

async def test_tools():
    """Test the agent tools system."""
    # Initialize tools
    register_default_tools(default_tool_factory)
    
    # List available tools
    logger.info(f"Available tools: {default_tool_factory.get_tool_types()}")
    
    # Test Gemini tool
    try:
        gemini_tool = GeminiTool()
        result = gemini_tool.run("Write a short paragraph about AI agents.")
        logger.info(f"Gemini tool result: {result}")
    except Exception as e:
        logger.error(f"Error testing Gemini tool: {e}")
    
    # Test Content Analyzer tool
    try:
        content = """
        # AI Agents: The Future of Automation
        
        Artificial intelligence agents are revolutionizing how we interact with technology. 
        These autonomous systems can perform tasks, make decisions, and learn from experience.
        
        ## Applications
        
        AI agents are used in customer service, data analysis, and creative content generation.
        They can work 24/7 without fatigue, providing consistent results.
        
        ## Challenges
        
        Despite their benefits, AI agents face challenges like ethical considerations,
        potential job displacement, and the need for human oversight.
        
        ## Conclusion
        
        As technology advances, AI agents will become more sophisticated and integrated into our daily lives.
        """
        
        content_analyzer = ContentAnalyzerTool()
        result = content_analyzer.run(content, analysis_type="full")
        logger.info(f"Content Analyzer tool result: {result}")
    except Exception as e:
        logger.error(f"Error testing Content Analyzer tool: {e}")
    
    # Test SEO Analyzer tool
    try:
        seo_analyzer = SEOAnalyzerTool()
        result = seo_analyzer.run(
            content=content,
            target_keyword="AI agents",
            analysis_type="full"
        )
        logger.info(f"SEO Analyzer tool result: {result}")
    except Exception as e:
        logger.error(f"Error testing SEO Analyzer tool: {e}")

if __name__ == "__main__":
    asyncio.run(test_tools())

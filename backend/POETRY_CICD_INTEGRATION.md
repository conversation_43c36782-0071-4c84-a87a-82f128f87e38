cd backend
./finalize_poetry_migration.shcd backend

This guide covers integrating Poetry into various CI/CD platforms for consistent dependency management across all environments.

## Table of Contents

1. [Overview](#overview)
2. [General Strategy](#general-strategy)
3. [Platform-Specific Configurations](#platform-specific-configurations)
   - [GitHub Actions](#github-actions)
   - [GitLab CI](#gitlab-ci)
   - [<PERSON>](#jen<PERSON>)
   - [CircleCI](#circleci)
   - [AWS CodeBuild](#aws-codebuild)
4. [Docker Integration](#docker-integration)
5. [Caching Strategy](#caching-strategy)
6. [Generating Configuration Files](#generating-configuration-files)

## Overview

Poetry improves CI/CD pipelines by providing:

- **Deterministic builds** through lock files
- **Faster installations** with dependency caching
- **Explicit dependency groups** for targeted installation
- **Simplified environment management**
- **Automated version resolution**

## General Strategy

Regardless of the CI/CD platform, follow these principles:

1. **Install Poetry** at the beginning of the pipeline
2. **Cache dependencies** to speed up builds
3. **Install only needed dependencies** (e.g., skip dev dependencies in production)
4. **Verify dependencies** match lock file
5. **Run tests** within the Poetry environment
6. **Build artifacts** (Docker images) using Poetry for dependency installation

## Platform-Specific Configurations

### GitHub Actions

```yaml
# Basic workflow structure for GitHub Actions
name: Python Poetry CI

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install Poetry
      run: |
        curl -sSL https://install.python-poetry.org | python3 -
        echo "$HOME/.local/bin" >> $GITHUB_PATH

    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pypoetry
        key: ${{ runner.os }}-poetry-${{ hashFiles('**/poetry.lock') }}

    - name: Install dependencies
      run: |
        cd backend
        poetry install

    - name: Run tests
      run: |
        cd backend
        poetry run pytest
```

### GitLab CI

```yaml
# Basic workflow structure for GitLab CI
image: python:3.11-slim

variables:
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.pip-cache"
  POETRY_CACHE_DIR: "$CI_PROJECT_DIR/.poetry-cache"

cache:
  key: 
    files:
      - poetry.lock
  paths:
    - .pip-cache/
    - .poetry-cache/

stages:
  - test
  - build
  - deploy

test:
  stage: test
  script:
    - cd backend
    - curl -sSL https://install.python-poetry.org | python3 -
    - export PATH="/root/.local/bin:$PATH"
    - poetry install
    - poetry run pytest
```

### Jenkins

```groovy
// Basic Jenkinsfile with Poetry
pipeline {
    agent {
        docker {
            image 'python:3.11-slim'
        }
    }
    
    environment {
        POETRY_HOME = "$HOME/.poetry"
        PATH = "$POETRY_HOME/bin:$PATH"
    }
    
    stages {
        stage('Setup') {
            steps {
                sh 'curl -sSL https://install.python-poetry.org | python3 -'
                sh 'cd backend && poetry install'
            }
        }
        
        stage('Test') {
            steps {
                sh 'cd backend && poetry run pytest'
            }
        }
    }
}
```

### CircleCI

```yaml
# Basic configuration for CircleCI
version: 2.1

orbs:
  python: circleci/python@2.1.1

jobs:
  test:
    docker:
      - image: cimg/python:3.11
    steps:
      - checkout
      - run:
          name: Install Poetry
          command: |
            curl -sSL https://install.python-poetry.org | python3 -
            echo 'export PATH="$HOME/.local/bin:$PATH"' >> $BASH_ENV
      - restore_cache:
          key: poetry-deps-{{ checksum "backend/poetry.lock" }}
      - run:
          name: Install dependencies
          command: |
            cd backend
            poetry install
      - save_cache:
          key: poetry-deps-{{ checksum "backend/poetry.lock" }}
          paths:
            - ~/.cache/pypoetry
      - run:
          name: Run tests
          command: |
            cd backend
            poetry run pytest
```

### AWS CodeBuild

```yaml
# buildspec.yml for AWS CodeBuild
version: 0.2

phases:
  install:
    runtime-versions:
      python: 3.11
    commands:
      - curl -sSL https://install.python-poetry.org | python3 -
      - export PATH="/root/.local/bin:$PATH"
      - cd backend
      - poetry config virtualenvs.create false
      - poetry install

  build:
    commands:
      - cd backend
      - poetry run pytest

cache:
  paths:
    - '/root/.cache/pypoetry/**/*'
```

## Docker Integration

For CI/CD pipelines that build Docker images, use our Poetry-based Dockerfile:

```dockerfile
# Multi-stage build using Poetry
FROM python:3.11-slim AS builder

WORKDIR /app
RUN pip install poetry
COPY poetry.lock pyproject.toml ./
RUN poetry export -f requirements.txt > requirements.txt

FROM python:3.11-slim
WORKDIR /app
COPY --from=builder /app/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0"]
```

## Caching Strategy

Effective caching is critical for efficient CI/CD pipelines:

### What to Cache

1. **Poetry Cache Directory**: `~/.cache/pypoetry` or custom location
2. **Virtual Environment**: Only if using in-project virtualenvs
3. **Dependencies**: In Docker builds, use multi-stage builds and layer caching

### Cache Keys

Base cache keys on the `poetry.lock` file to ensure cache invalidation when dependencies change:

```
cache-key: v1-poetry-deps-{{ checksum "poetry.lock" }}
```

## Generating Configuration Files

We provide a utility to generate CI/CD configuration files for your project:

```bash
cd backend
./poetry_ci_integration.py github  # or gitlab, jenkins, circle, aws
```

The generated configurations include:

- Proper Poetry installation
- Dependency caching
- Testing and linting
- Docker image building (where applicable)
- Deployment steps (placeholder)

## Best Practices

1. **Keep Poetry Version Consistent**: Use the same Poetry version across all environments
2. **Use Lock Files**: Always commit `poetry.lock` to ensure deterministic builds
3. **Specify Python Version**: Explicitly set Python version in CI config
4. **Skip Virtual Environments**: In CI, consider setting `virtualenvs.create=false`
5. **Export Requirements**: For multi-stage Docker builds, export from Poetry to requirements.txt

## Troubleshooting

### Common Issues

1. **Poetry not found in PATH**: Add Poetry's bin directory to PATH
2. **Dependency installation fails**: Ensure all system dependencies are installed
3. **Cache not working**: Verify cache key and paths are correct
4. **Tests fail in CI but pass locally**: Check Python version and environment variables

### Debug Techniques

```bash
# Show Poetry configuration
poetry config --list

# Verbose installation
poetry install -v

# Check dependency tree
poetry show --tree
```

---

For any issues with CI/CD integration, please open an issue in the project repository.
# Poetry-specific ignores
.venv/
__pycache__/
poetry_migration_progress.json

# Poetry cache
.poetry-cache/
.pip-cache/

# Virtual environments
.venv/
venv/
ENV/

# Python bytecode
*.py[cod]
*$py.class
__pycache__/

# Distribution / packaging
dist/
build/
*.egg-info/

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# Environments
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# mypy
.mypy_cache/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
.DS_Store
Thumbs.db
#!/usr/bin/env python3
"""
Poetry Dependency Organizer Script

This script organizes dependencies in pyproject.toml into logical groups
by directly using Poetry CLI commands rather than parsing the TOML file.
"""

import os
import sys
import subprocess
from pathlib import Path

# Define dependency groups
DEPENDENCY_GROUPS = {
    "web": [
        "fastapi",
        "uvicorn",
        "pydantic",
        "pydantic-settings",
        "starlette",
        "httpx",
        "python-multipart",
        "jinja2",
        "email-validator",
    ],
    "database": [
        "sqlalchemy",
        "alembic",
        "psycopg2-binary",
        "asyncpg",
        "databases",
        "sqlite",
        "sqlmodel",
    ],
    "ai": [
        # No langchain dependencies - completely removed
        "openai",
        "google-generativeai",
        "crewai",
        "anthropic",
        "pinecone-client",
        "chromadb",
    ],
    "security": [
        "python-jose",
        "passlib",
        "bcrypt",
        "cryptography",
        "pyjwt",
    ],
    "dev": [
        "pytest",
        "black",
        "flake8",
        "mypy",
        "isort",
        "ipython",
        "jupyter",
        "pytest-cov",
    ],
}

# Define version constraints for common packages
VERSION_CONSTRAINTS = {
    "fastapi": "^0.115.0",
    "uvicorn": "^0.34.0[standard]",
    "pydantic": "^2.0.0",
    "pydantic-settings": "^2.0.0",
    "sqlalchemy": "^2.0.0",
    "alembic": "^1.15.0",
    "psycopg2-binary": "^2.9.0",
    # Removed all langchain dependencies
    "openai": "^1.0.0",
    "google-generativeai": "^0.3.0",
    "python-jose": "^3.3.0",
    "passlib": "^1.7.4",
}

# List of dependencies to explicitly remove
REMOVE_DEPENDENCIES = [
    "langchain",
    "langchain-core",
    "langchain-community",
    "langchain-openai",
    "tiktoken"  # Also remove tiktoken
]

def run_command(cmd, cwd=None):
    """Run a shell command and return the output"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            check=True, 
            cwd=cwd,
            capture_output=True,
            text=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Error executing command: {cmd}")
        print(f"Error: {e}")
        print(f"Output: {e.stdout}")
        print(f"Error output: {e.stderr}")
        return None

def main():
    print("=== Poetry Dependency Organizer ===")
    
    # Check if poetry is installed
    if run_command("poetry --version") is None:
        print("Poetry is not installed. Please install it first.")
        sys.exit(1)
    
    # Create backup of pyproject.toml
    print("Creating backup of pyproject.toml...")
    run_command("cp pyproject.toml pyproject.toml.bak")
    
    # Remove banned dependencies
    print("Removing banned dependencies...")
    for dep in REMOVE_DEPENDENCIES:
        print(f"Removing {dep}...")
        run_command(f"poetry remove {dep} --group '*' 2>/dev/null || true")
    
    # Ensure all groups exist
    print("Creating dependency groups...")
    for group in DEPENDENCY_GROUPS.keys():
        # Check if group exists by trying to add a dummy dependency and then removing it
        print(f"Ensuring group '{group}' exists...")
        if group != "dev":  # Skip for dev group as it exists by default
            run_command(f"poetry group add {group} || true")
    
    # Move dependencies to their correct groups
    print("Organizing dependencies into groups...")
    
    # Get list of all dependencies
    deps_output = run_command("poetry show --no-ansi")
    if deps_output:
        # Parse the output to get dependency names
        all_deps = []
        for line in deps_output.split('\n'):
            if line.strip() and not line.startswith(' '):
                dep_name = line.split(' ')[0]
                all_deps.append(dep_name)
        
        # Process each dependency
        for dep in all_deps:
            # Skip banned dependencies
            if dep in REMOVE_DEPENDENCIES:
                continue
                
            # Find which group this dependency belongs to
            target_group = None
            for group_name, group_deps in DEPENDENCY_GROUPS.items():
                if dep in group_deps:
                    target_group = group_name
                    break
            
            if target_group:
                # Check if we have a specific version constraint
                version = VERSION_CONSTRAINTS.get(dep, None)
                
                # Remove from main dependencies
                print(f"Moving {dep} to {target_group} group...")
                run_command(f"poetry remove {dep} 2>/dev/null || true")
                
                # Add to target group with specific version if available
                if version:
                    run_command(f"poetry add {dep}:{version} --group {target_group}")
                else:
                    run_command(f"poetry add {dep} --group {target_group}")
    
    print("\n=== Dependency Organization Complete ===")
    print("The following dependency groups are now available:")
    
    # Show dependency counts per group
    for group in DEPENDENCY_GROUPS.keys():
        deps = run_command(f"poetry show --group {group} --no-ansi | wc -l")
        if deps and deps.isdigit():
            print(f"  - {group}: {deps.strip()} dependencies")
    
    print("\nYou can now install specific groups with:")
    print("  poetry install --only main,web,database  # For minimal install")
    print("  poetry install  # For full install with all groups")

if __name__ == "__main__":
    main()
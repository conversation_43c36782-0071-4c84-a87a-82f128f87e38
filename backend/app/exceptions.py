import logging

from fastapi import Request
from fastapi.exception_handlers import RequestValidationError
from fastapi.exceptions import HTTPException as FastAPIHTTPException
from fastapi.responses import JSONResponse


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    logging.warning(f"[VALIDATION ERROR] {request.method} {request.url} | {exc}")
    return JSONResponse(
        status_code=422, content={"detail": exc.errors(), "body": exc.body}
    )


async def http_exception_handler(request: Request, exc: FastAPIHTTPException):
    logging.warning(f"[HTTP ERROR] {request.method} {request.url} | {exc.detail}")
    return J<PERSON>NResponse(status_code=exc.status_code, content={"detail": exc.detail})

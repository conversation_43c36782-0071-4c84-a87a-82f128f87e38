"""
History store module for tracking and retrieving reasoning traces.

This module provides functions for saving traces to the database
and retrieving them for analysis and debugging.
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Generator
from sqlalchemy.orm import Session
from sqlalchemy import desc, func

from app.db.models import ReasoningTrace, TraceStep
from app.core.config import settings
from app.db.session import get_db, get_redis_cache

logger = logging.getLogger(__name__)

def save_trace_to_history(
    request_id: str,
    user_prompt: str,
    result: str,
    trace: Dict[str, Any],
    db: Optional[Session] = None
) -> str:
    """
    Save a reasoning trace to the database with indexable metadata.

    Args:
        request_id: The unique ID for the request
        user_prompt: The original user prompt
        result: The generated content result
        trace: The full reasoning trace data
        db: Optional database session

    Returns:
        The request ID
    """
    close_db = False
    if db is None:
        db = next(get_db())
        close_db = True

    try:
        # Extract metadata from trace for indexed fields
        process_type = trace.get("process_type", "unknown")
        complexity_score = trace.get("complexity_score", 0)
        request_type = trace.get("request_type", "unknown")
        total_steps = trace.get("total_steps", 0)
        duration_ms = trace.get("duration_ms", 0)

        # Create the main trace record
        trace_record = ReasoningTrace(
            request_id=request_id,
            user_prompt=user_prompt,
            result=result,
            process_type=process_type,
            complexity_score=complexity_score,
            request_type=request_type,
            total_steps=total_steps,
            duration_ms=duration_ms,
            created_at=datetime.utcnow()
        )

        # Set the trace data as JSON
        trace_record.set_trace_json(trace)

        # Add to database
        db.add(trace_record)
        db.flush()  # Flush to get the ID

        # Add individual step records for detailed queries
        steps = trace.get("steps", [])
        for step in steps:
            # Convert timestamp safely
            try:
                timestamp_value = step.get("timestamp", 0)
                if isinstance(timestamp_value, int) and timestamp_value > 1000000000000:  # Likely milliseconds
                    timestamp_value = timestamp_value / 1000  # Convert to seconds
                timestamp = datetime.fromtimestamp(timestamp_value)
            except (ValueError, OverflowError, OSError):
                # Fallback to current time if timestamp is invalid
                timestamp = datetime.utcnow()

            step_record = TraceStep(
                trace_id=trace_record.id,
                request_id=request_id,
                step_id=step.get("step_id", 0),
                agent=step.get("agent", "Unknown"),
                type=step.get("type", "unknown"),
                timestamp=timestamp,
                task_id=step.get("task_id"),
                content=step.get("content") if step.get("type") == "message" else None,
                tool_name=step.get("tool") if step.get("type") == "tool_use" else None,
                success=step.get("success") if step.get("type") == "tool_use" else None
            )
            db.add(step_record)

        # Commit all changes
        db.commit()
        logger.info(f"Saved trace for request {request_id} with {len(steps)} steps")

        # Add to cache if enabled
        cache_trace_if_enabled(request_id, trace)

        return request_id

    except Exception as e:
        db.rollback()
        logger.error(f"Error saving trace for request {request_id}: {str(e)}", exc_info=True)
        raise
    finally:
        if close_db:
            db.close()


def get_trace(request_id: str, use_cache: bool = True) -> Optional[Dict[str, Any]]:
    """
    Retrieve a complete trace by request ID.

    Args:
        request_id: The unique ID for the request
        use_cache: Whether to check cache before database

    Returns:
        The full trace data or None if not found
    """
    # Check cache first if enabled and requested
    if use_cache and hasattr(settings, "ENABLE_TRACE_CACHE") and settings.ENABLE_TRACE_CACHE:
        cached_trace = get_cached_trace(request_id)
        if cached_trace is not None:
            logger.debug(f"Retrieved trace {request_id} from cache")
            return cached_trace

    # Query database if not in cache or cache disabled
    db = next(get_db())
    try:
        trace_record = db.query(ReasoningTrace).filter(
            ReasoningTrace.request_id == request_id
        ).first()

        if not trace_record:
            logger.warning(f"Trace not found for request ID: {request_id}")
            return None

        # Get the complete trace from JSON data
        trace_data = trace_record.get_trace_json()

        # Cache the result if caching is enabled
        if hasattr(settings, "ENABLE_TRACE_CACHE") and settings.ENABLE_TRACE_CACHE:
            cache_trace_if_enabled(request_id, trace_data)

        return trace_data

    except Exception as e:
        logger.error(f"Error retrieving trace {request_id}: {str(e)}", exc_info=True)
        return None
    finally:
        db.close()


def get_recent_traces(
    limit: int = 10,
    offset: int = 0,
    user_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Get summaries of recent reasoning traces with pagination and filtering.

    Args:
        limit: Maximum number of traces to return
        offset: Offset for pagination
        user_id: Optional user ID to filter results

    Returns:
        List of trace summaries
    """
    db = next(get_db())
    try:
        query = db.query(ReasoningTrace)

        # Apply user_id filter if provided
        if user_id:
            query = query.filter(ReasoningTrace.user_id == user_id)

        # Apply ordering and pagination
        query = query.order_by(desc(ReasoningTrace.created_at))
        query = query.offset(offset).limit(limit)

        traces = query.all()
        return [trace.to_dict() for trace in traces]

    except Exception as e:
        logger.error(f"Error retrieving recent traces: {str(e)}", exc_info=True)
        return []
    finally:
        db.close()


def get_traces_count(user_id: Optional[str] = None) -> int:
    """
    Get the total count of traces, optionally filtered by user_id.

    Args:
        user_id: Optional user ID to filter results

    Returns:
        Total count of traces
    """
    db = next(get_db())
    try:
        query = db.query(func.count(ReasoningTrace.id))

        # Apply user_id filter if provided
        if user_id:
            query = query.filter(ReasoningTrace.user_id == user_id)

        return query.scalar() or 0

    except Exception as e:
        logger.error(f"Error counting traces: {str(e)}", exc_info=True)
        return 0
    finally:
        db.close()


def get_traces_by_criteria(
    request_type: Optional[str] = None,
    agent: Optional[str] = None,
    min_complexity: Optional[float] = None,
    max_complexity: Optional[float] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    limit: int = 50,
    offset: int = 0
) -> List[Dict[str, Any]]:
    """
    Get traces matching specified criteria for analytics.

    Args:
        request_type: Filter by request type
        agent: Filter by agent involved
        min_complexity: Minimum complexity score
        max_complexity: Maximum complexity score
        start_date: Start of date range
        end_date: End of date range
        limit: Maximum number of traces to return
        offset: Offset for pagination

    Returns:
        List of matching trace summaries
    """
    db = next(get_db())
    try:
        query = db.query(ReasoningTrace)

        # Apply filters
        if request_type:
            query = query.filter(ReasoningTrace.request_type == request_type)

        if min_complexity is not None:
            query = query.filter(ReasoningTrace.complexity_score >= min_complexity)

        if max_complexity is not None:
            query = query.filter(ReasoningTrace.complexity_score <= max_complexity)

        if start_date:
            query = query.filter(ReasoningTrace.created_at >= start_date)

        if end_date:
            query = query.filter(ReasoningTrace.created_at <= end_date)

        # Filter by agent if specified (requires join with TraceStep)
        if agent:
            query = query.join(TraceStep).filter(TraceStep.agent == agent).distinct()

        # Order by date and apply pagination
        query = query.order_by(desc(ReasoningTrace.created_at)).offset(offset).limit(limit)

        traces = query.all()
        return [trace.to_dict() for trace in traces]

    except Exception as e:
        logger.error(f"Error retrieving traces by criteria: {str(e)}", exc_info=True)
        return []
    finally:
        db.close()


def get_trace_steps(
    request_id: str,
    agent: Optional[str] = None,
    step_type: Optional[str] = None,
    limit: int = 100
) -> List[Dict[str, Any]]:
    """
    Get individual steps from a trace for detailed analysis.

    Args:
        request_id: The unique ID for the request
        agent: Filter by specific agent
        step_type: Filter by step type (message, tool_use, etc.)
        limit: Maximum number of steps to return

    Returns:
        List of steps from the trace
    """
    db = next(get_db())
    try:
        query = db.query(TraceStep).filter(TraceStep.request_id == request_id)

        if agent:
            query = query.filter(TraceStep.agent == agent)

        if step_type:
            query = query.filter(TraceStep.type == step_type)

        steps = query.order_by(TraceStep.step_id).limit(limit).all()
        return [step.to_dict() for step in steps]

    except Exception as e:
        logger.error(f"Error retrieving steps for trace {request_id}: {str(e)}", exc_info=True)
        return []
    finally:
        db.close()


def get_agent_performance_metrics(
    agent_name: str,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None
) -> Dict[str, Any]:
    """
    Get performance metrics for a specific agent.

    Args:
        agent_name: The name of the agent to analyze
        start_date: Start of date range
        end_date: End of date range

    Returns:
        Dictionary of performance metrics
    """
    db = next(get_db())
    try:
        # Base query for steps by this agent
        query = db.query(TraceStep).filter(TraceStep.agent == agent_name)

        if start_date:
            query = query.filter(TraceStep.timestamp >= start_date)

        if end_date:
            query = query.filter(TraceStep.timestamp <= end_date)

        # Get total steps
        total_steps = query.count()

        # Get tool usage stats
        tool_usage = query.filter(TraceStep.type == "tool_use")
        total_tool_uses = tool_usage.count()
        successful_tool_uses = tool_usage.filter(TraceStep.success == True).count()

        # Get unique traces this agent participated in
        unique_traces = db.query(func.count(func.distinct(TraceStep.trace_id))).filter(
            TraceStep.agent == agent_name
        ).scalar()

        return {
            "agent": agent_name,
            "total_steps": total_steps,
            "total_tool_uses": total_tool_uses,
            "successful_tool_uses": successful_tool_uses,
            "tool_success_rate": (successful_tool_uses / total_tool_uses) if total_tool_uses > 0 else 0,
            "unique_traces": unique_traces,
            "start_date": start_date.isoformat() if start_date else None,
            "end_date": end_date.isoformat() if end_date else None
        }

    except Exception as e:
        logger.error(f"Error retrieving metrics for agent {agent_name}: {str(e)}", exc_info=True)
        return {
            "agent": agent_name,
            "error": str(e)
        }
    finally:
        db.close()


# Cache-related functions

def cache_trace_if_enabled(request_id: str, trace: Dict[str, Any]) -> bool:
    """
    Cache a trace if caching is enabled.

    Args:
        request_id: The unique ID for the request
        trace: The trace data to cache

    Returns:
        True if cached, False otherwise
    """
    if not hasattr(settings, "ENABLE_TRACE_CACHE") or not settings.ENABLE_TRACE_CACHE:
        return False

    try:
        cache = get_redis_cache()
        if cache:
            ttl = getattr(settings, "TRACE_CACHE_TTL", 3600)  # Default 1 hour
            cache_key = f"trace:{request_id}"
            cache.set(cache_key, json.dumps(trace), ex=ttl)
            logger.debug(f"Cached trace {request_id} with TTL {ttl}s")
            return True
        return False
    except Exception as e:
        logger.warning(f"Failed to cache trace {request_id}: {str(e)}")
        return False


def get_cached_trace(request_id: str) -> Optional[Dict[str, Any]]:
    """
    Get a trace from the cache.

    Args:
        request_id: The unique ID for the request

    Returns:
        The cached trace data or None if not found
    """
    try:
        cache = get_redis_cache()
        if not cache:
            return None

        cache_key = f"trace:{request_id}"
        cached_data = cache.get(cache_key)

        if not cached_data:
            return None

        return json.loads(cached_data)
    except Exception as e:
        logger.warning(f"Failed to retrieve cached trace {request_id}: {str(e)}")
        return None


def invalidate_trace_cache(request_id: str) -> bool:
    """
    Invalidate a cached trace.

    Args:
        request_id: The unique ID for the request

    Returns:
        True if invalidated, False otherwise
    """
    try:
        cache = get_redis_cache()
        if not cache:
            return False

        cache_key = f"trace:{request_id}"
        cache.delete(cache_key)
        logger.debug(f"Invalidated cache for trace {request_id}")
        return True
    except Exception as e:
        logger.warning(f"Failed to invalidate cache for trace {request_id}: {str(e)}")
        return False
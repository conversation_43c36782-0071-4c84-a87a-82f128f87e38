"""
Database session manager for SQLAlchemy and Redis cache
"""

import logging
import os
import pathlib
from typing import Generator, Optional

from sqlalchemy import create_engine, event
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
import redis

from app.core.config import settings
from app.db.models import Base

logger = logging.getLogger(__name__)

# Configure SQLAlchemy engine with appropriate connection pooling
def create_db_engine():
    """Create and configure the SQLAlchemy engine with proper settings based on database type."""
    connect_args = {}
    pool_options = {}

    # SQLite-specific configuration
    if settings.DATABASE_URL.startswith('sqlite:///'):
        # Extract the database file path from the URL
        db_path = settings.DATABASE_URL.replace('sqlite:///', '')

        # Handle relative paths
        if not db_path.startswith('/'):
            # Convert to absolute path relative to the current working directory
            db_path = os.path.abspath(db_path)

        # Ensure the directory exists
        db_dir = os.path.dirname(db_path)
        if db_dir:
            pathlib.Path(db_dir).mkdir(parents=True, exist_ok=True)
            logger.info(f"Ensured database directory exists: {db_dir}")

        # SQLite-specific connection arguments
        connect_args = {"check_same_thread": False}

        # Update the DATABASE_URL with the absolute path
        db_url = f"sqlite:///{db_path}"
        logger.info(f"Using SQLite database at: {db_path}")
    else:
        # For other databases (PostgreSQL, MySQL, etc.)
        db_url = settings.DATABASE_URL

        # Configure connection pooling for production databases
        pool_options = {
            "poolclass": QueuePool,
            "pool_size": 5,
            "max_overflow": 10,
            "pool_timeout": 30,
            "pool_recycle": 1800,  # Recycle connections after 30 minutes
        }

    # Create the engine with appropriate configuration
    engine = create_engine(
        db_url,
        connect_args=connect_args,
        **pool_options
    )

    # Enable SQLite foreign key support
    if settings.DATABASE_URL.startswith('sqlite:///'):
        @event.listens_for(engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            cursor = dbapi_connection.cursor()
            cursor.execute("PRAGMA foreign_keys=ON")
            cursor.close()

    return engine

# Create SQLAlchemy engine and session
engine = create_db_engine()
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create database tables if they don't exist
try:
    # Create all tables
    Base.metadata.create_all(bind=engine)
    logger.info("Database tables initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize database tables: {str(e)}")
    logger.exception(e)  # Log the full exception traceback for debugging

# Redis client
redis_client = None
if settings.REDIS_URL:
    try:
        redis_client = redis.Redis.from_url(
            settings.REDIS_URL,
            decode_responses=True,
            socket_timeout=5.0,  # Add timeout to prevent hanging connections
            socket_connect_timeout=5.0
        )
        # Test the connection
        redis_client.ping()
        logger.info("Redis cache connection established and tested successfully")
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {str(e)}")
        logger.exception(e)  # Log the full exception traceback for debugging
        redis_client = None


def get_db() -> Generator[Session, None, None]:
    """
    Get a database session.

    This function creates a new SQLAlchemy session that will be used for a single request
    and then closed when the request is complete. It's designed to be used with FastAPI's
    dependency injection system.

    Returns:
        Generator[Session, None, None]: A SQLAlchemy session

    Example:
        ```python
        @app.get("/items/")
        def read_items(db: Session = Depends(get_db)):
            return db.query(Item).all()
        ```
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {str(e)}")
        logger.exception(e)
        db.rollback()  # Rollback any failed transactions
        raise
    finally:
        db.close()
        logger.debug("Database session closed")


def get_database_url() -> str:
    """
    Get the database URL for direct connections.

    Returns:
        str: The database URL
    """
    if settings.DATABASE_URL.startswith('sqlite:///'):
        # Extract the database file path from the URL
        db_path = settings.DATABASE_URL.replace('sqlite:///', '')

        # Handle relative paths
        if not db_path.startswith('/'):
            # Convert to absolute path relative to the current working directory
            db_path = os.path.abspath(db_path)

        return f"sqlite:///{db_path}"
    else:
        return settings.DATABASE_URL


def get_redis_cache() -> Optional[redis.Redis]:
    """
    Get Redis client if available.

    This function returns the configured Redis client if it's available,
    or None if Redis is not configured or the connection failed.
    It's designed to be used with FastAPI's dependency injection system.

    Returns:
        Optional[redis.Redis]: A Redis client or None

    Example:
        ```python
        @app.get("/cached-items/")
        def read_cached_items(cache: Optional[redis.Redis] = Depends(get_redis_cache)):
            if cache:
                cached_data = cache.get("items")
                if cached_data:
                    return json.loads(cached_data)
            # Fallback if cache is not available or data is not cached
            return {"items": []}
        ```
    """
    if redis_client is None:
        logger.debug("Redis client not available")
        return None

    try:
        # Verify the connection is still alive
        redis_client.ping()
        return redis_client
    except Exception as e:
        logger.warning(f"Redis connection check failed: {str(e)}")
        return None
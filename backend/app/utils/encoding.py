"""
Simple token counting without tiktoken dependency
"""

from typing import Optional, Any, List, Dict
from typing_extensions import Protocol

def count_tokens(text: str, model: Optional[str] = None) -> int:
    """
    Estimates token count without using tiktoken.
    
    Args:
        text: Text to count tokens for
        model: Ignored, included for compatibility
        
    Returns:
        Approximate token count
    """
    if not text:
        return 0
        
    # For English text, ~4 chars per token is a reasonable approximation
    return max(1, len(text) // 4)

# Define encoder interface using Protocol for proper typing
class Encoder(Protocol):
    def encode(self, text: str) -> List[int]:
        """Encode text to tokens."""
        ...
    
    def decode(self, tokens: List[int]) -> str:
        """Decode tokens to text."""
        ...

def get_encoding(model: Optional[str] = None) -> Encoder:
    """
    Compatibility function to mimic tiktoken interface.
    
    Args:
        model: Ignored, included for compatibility
        
    Returns:
        A simple encoder object
    """
    class SimpleEncoder:
        def encode(self, text: str) -> List[int]:
            # Return placeholder tokens (1 per 4 chars)
            return [0] * count_tokens(text)
            
        def decode(self, tokens: List[int]) -> str:
            return "[Decoded text placeholder]"
    
    return SimpleEncoder()

# Add common model aliases for compatibility
MODELS: Dict[str, int] = {
    "gpt-4": 8192,
    "gpt-4-0314": 8192,
    "gpt-4-0613": 8192,
    "gpt-4-32k": 32768,
    "gpt-4-32k-0314": 32768,
    "gpt-4-32k-0613": 32768,
    "gpt-3.5-turbo": 4096,
    "gpt-3.5-turbo-0301": 4096,
    "gpt-3.5-turbo-0613": 4096,
    "gpt-3.5-turbo-16k": 16384,
    "gpt-3.5-turbo-16k-0613": 16384,
    "text-embedding-ada-002": 8191,
}

def get_max_tokens(model: Optional[str]) -> int:
    """Get max tokens for a model."""
    if model is None:
        return 4096
    # Now model is definitely a string, not None
    return MODELS.get(model, 4096)  # Default to 4096

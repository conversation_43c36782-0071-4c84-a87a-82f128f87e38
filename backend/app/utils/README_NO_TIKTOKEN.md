# Token Counting Without Tiktoken

This module provides a simple token counting solution that doesn't require tiktoken.

## Why tiktoken was removed:

1. It requires Rust compiler for installation
2. It causes Poetry and pip dependency resolution issues
3. It's not necessary for most use cases where approximate token counts are sufficient

## Usage:

```python
from app.utils.encoding import count_tokens, get_encoding

# Simple token counting
tokens = count_tokens("This is a test message")

# For code expecting tiktoken API:
enc = get_encoding()
encoded = enc.encode("This is some text")
token_count = len(encoded)
```

This implementation provides an approximation based on character count (roughly 4 characters per token for English text).

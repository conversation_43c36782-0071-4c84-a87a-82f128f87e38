"""
Focus Group Simulator Service
Handles the business logic for focus group simulation and text assistance.
"""
import logging
import random
import time
import os
from datetime import datetime
from typing import List, Dict, Any, Optional

try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False

from app.schemas.focus_group import (
    FocusGroupRequest, FocusGroupResponse, FocusGroupSimulation,
    TextAssistRequest, TextAssistResponse,
    Participant, Comment, Discussion, DemographicPattern,
    SentimentAnalysis, SentimentBreakdown, FocusGroupSummary
)

logger = logging.getLogger(__name__)


class FocusGroupService:
    """Service for handling focus group simulations and text assistance."""

    def __init__(self):
        self.participant_templates = self._create_participant_templates()
        self.question_templates = self._create_question_templates()
        self.gemini_model = self._initialize_gemini()

    def _initialize_gemini(self):
        """Initialize Gemini AI model if available."""
        if not GEMINI_AVAILABLE:
            logger.warning("Gemini AI not available. Install google-generativeai package.")
            return None

        api_key = os.environ.get("GEMINI_API_KEY")
        if not api_key:
            logger.warning("GEMINI_API_KEY not found. Focus group will use simulated responses.")
            return None

        try:
            genai.configure(api_key=api_key)
            # Try different model names that are available
            model_names = ['gemini-1.5-flash', 'gemini-1.5-pro', 'gemini-pro']

            for model_name in model_names:
                try:
                    model = genai.GenerativeModel(model_name)
                    # Test the model with a simple prompt
                    test_response = model.generate_content("Test")
                    logger.info(f"Gemini AI initialized successfully with model: {model_name}")
                    return model
                except Exception as model_error:
                    logger.warning(f"Model {model_name} not available: {str(model_error)}")
                    continue

            logger.error("No available Gemini models found")
            return None

        except Exception as e:
            logger.error(f"Failed to initialize Gemini AI: {str(e)}")
            return None

    def _create_participant_templates(self) -> List[Dict[str, Any]]:
        """Create diverse participant templates for the focus group."""
        return [
            {
                "name": "María González",
                "age_range": "25-34",
                "gender": "Femenino",
                "education": "Universitaria",
                "income_level": "Medio-alto",
                "digital_usage": "Avanzado",
                "interests": ["tecnología", "viajes", "fitness"],
                "personality_traits": ["analítica", "detallista", "cautelosa"],
                "buying_behavior": {
                    "price_sensitivity": "Moderada",
                    "brand_loyalty": "Alta",
                    "research_level": "Extensivo",
                    "decision_speed": "Lenta",
                    "influencer_impact": "Medio"
                }
            },
            {
                "name": "Carlos Rodríguez",
                "age_range": "35-44",
                "gender": "Masculino",
                "education": "Técnica",
                "income_level": "Medio",
                "digital_usage": "Intermedio",
                "interests": ["deportes", "familia", "automóviles"],
                "personality_traits": ["práctico", "directo", "tradicional"],
                "buying_behavior": {
                    "price_sensitivity": "Alta",
                    "brand_loyalty": "Media",
                    "research_level": "Básico",
                    "decision_speed": "Rápida",
                    "influencer_impact": "Bajo"
                }
            },
            {
                "name": "Ana Martínez",
                "age_range": "18-24",
                "gender": "Femenino",
                "education": "Estudiante",
                "income_level": "Bajo",
                "digital_usage": "Nativo digital",
                "interests": ["redes sociales", "moda", "música"],
                "personality_traits": ["espontánea", "social", "influenciable"],
                "buying_behavior": {
                    "price_sensitivity": "Muy alta",
                    "brand_loyalty": "Baja",
                    "research_level": "Social",
                    "decision_speed": "Muy rápida",
                    "influencer_impact": "Muy alto"
                }
            },
            {
                "name": "Roberto Silva",
                "age_range": "45-54",
                "gender": "Masculino",
                "education": "Posgrado",
                "income_level": "Alto",
                "digital_usage": "Básico",
                "interests": ["negocios", "golf", "vinos"],
                "personality_traits": ["conservador", "exigente", "escéptico"],
                "buying_behavior": {
                    "price_sensitivity": "Baja",
                    "brand_loyalty": "Muy alta",
                    "research_level": "Profesional",
                    "decision_speed": "Muy lenta",
                    "influencer_impact": "Muy bajo"
                }
            },
            {
                "name": "Laura Fernández",
                "age_range": "25-34",
                "gender": "Femenino",
                "education": "Universitaria",
                "income_level": "Medio-alto",
                "digital_usage": "Avanzado",
                "interests": ["sostenibilidad", "yoga", "cocina"],
                "personality_traits": ["consciente", "equilibrada", "reflexiva"],
                "buying_behavior": {
                    "price_sensitivity": "Media",
                    "brand_loyalty": "Media",
                    "research_level": "Ético",
                    "decision_speed": "Media",
                    "influencer_impact": "Medio"
                }
            },
            {
                "name": "Diego Morales",
                "age_range": "35-44",
                "gender": "Masculino",
                "education": "Técnica",
                "income_level": "Medio",
                "digital_usage": "Intermedio",
                "interests": ["tecnología", "gaming", "ciencia ficción"],
                "personality_traits": ["innovador", "curioso", "early adopter"],
                "buying_behavior": {
                    "price_sensitivity": "Media",
                    "brand_loyalty": "Baja",
                    "research_level": "Técnico",
                    "decision_speed": "Rápida",
                    "influencer_impact": "Alto"
                }
            }
        ]

    def _create_question_templates(self) -> List[str]:
        """Create default questions for focus group discussions."""
        return [
            "¿Cuál es tu primera impresión sobre este contenido/producto?",
            "¿Qué aspectos te resultan más atractivos y por qué?",
            "¿Hay algo que te genera dudas o preocupaciones?",
            "¿Cómo compararías esto con alternativas que conoces?",
            "¿Recomendarías esto a un amigo o familiar? ¿Por qué?",
            "¿Qué cambiarías o mejorarías?",
            "¿En qué situaciones usarías o comprarías esto?",
            "¿El precio te parece justo para lo que ofrece?"
        ]

    def _select_participants(self, num_participants: int) -> List[Participant]:
        """Select and create participants for the focus group."""
        selected_templates = random.sample(
            self.participant_templates,
            min(num_participants, len(self.participant_templates))
        )

        participants = []
        for i, template in enumerate(selected_templates):
            participant = Participant(
                id=i + 1,
                **template
            )
            participants.append(participant)

        return participants

    async def simulate_focus_group(self, request: FocusGroupRequest) -> FocusGroupResponse:
        """
        Simulate a complete focus group session.

        Args:
            request: Focus group simulation request

        Returns:
            Complete focus group simulation results
        """
        try:
            logger.info(f"Starting focus group simulation for content: {request.content[:100]}...")

            # Select participants
            participants = self._select_participants(request.num_participants or 6)

            # Determine questions to use
            questions = request.questions if request.questions else random.sample(
                self.question_templates, min(5, len(self.question_templates))
            )

            # Generate discussions for each question
            discussions = []
            for question in questions:
                discussion = await self._generate_discussion(
                    question, participants, request.content, request.context
                )
                discussions.append(discussion)

            # Generate summary and insights
            summary = await self._generate_summary(discussions, participants, request.content)

            # Create simulation result
            simulation = FocusGroupSimulation(
                discussions=discussions,
                summary=summary
            )

            return FocusGroupResponse(
                status="success",
                focus_group_simulation=simulation,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error(f"Error in focus group simulation: {str(e)}")
            return FocusGroupResponse(
                status="error",
                error_message=f"Error en la simulación: {str(e)}",
                timestamp=datetime.now().isoformat()
            )

    async def _generate_discussion(
        self,
        question: str,
        participants: List[Participant],
        content: str,
        context: Optional[str]
    ) -> Discussion:
        """Generate a realistic discussion for a specific question."""

        # Simulate realistic conversation flow
        conversation = []

        # Randomize participant order for natural flow
        speaking_order = random.sample(participants, len(participants))

        for i, participant in enumerate(speaking_order):
            # Generate response based on participant profile
            comment_text = await self._generate_participant_response(
                participant, question, content, context, i
            )

            # Determine sentiment based on content and participant traits
            sentiment = self._determine_sentiment(participant, content, comment_text)

            comment = Comment(
                participant_id=participant.id,
                participant_name=participant.name,
                comment=comment_text,
                sentiment=sentiment
            )
            conversation.append(comment)

        return Discussion(
            question=question,
            conversation=conversation
        )

    async def _generate_participant_response(
        self,
        participant: Participant,
        question: str,
        content: str,
        context: Optional[str],
        position: int
    ) -> str:
        """Generate a realistic response based on participant profile using Gemini AI."""

        if self.gemini_model:
            return await self._generate_ai_response(participant, question, content, context)
        else:
            raise Exception("Gemini AI no está disponible. Por favor configura GEMINI_API_KEY para usar respuestas reales.")

    async def _generate_ai_response(
        self,
        participant: Participant,
        question: str,
        content: str,
        context: Optional[str]
    ) -> str:
        """Generate response using Gemini AI."""
        try:
            # Create detailed prompt for Gemini
            prompt = f"""
Eres {participant.name}, una persona con las siguientes características:
- Edad: {participant.age_range} años
- Género: {participant.gender}
- Educación: {participant.education}
- Nivel de ingresos: {participant.income_level}
- Uso digital: {participant.digital_usage}
- Intereses: {', '.join(participant.interests)}
- Personalidad: {', '.join(participant.personality_traits)}
- Comportamiento de compra:
  * Sensibilidad al precio: {participant.buying_behavior.get('price_sensitivity', 'Media')}
  * Lealtad a marcas: {participant.buying_behavior.get('brand_loyalty', 'Media')}
  * Nivel de investigación: {participant.buying_behavior.get('research_level', 'Básico')}
  * Velocidad de decisión: {participant.buying_behavior.get('decision_speed', 'Media')}
  * Impacto de influencers: {participant.buying_behavior.get('influencer_impact', 'Medio')}

Estás participando en un focus group sobre: "{content}"
{f"Contexto adicional: {context}" if context else ""}

Pregunta del moderador: "{question}"

Responde como {participant.name} respondería naturalmente, considerando tu perfil demográfico y personalidad.
Tu respuesta debe ser:
- Auténtica y natural (como hablaría una persona real)
- Coherente con tu perfil demográfico
- Entre 1-3 oraciones
- En español
- Sin mencionar explícitamente tus características demográficas

Respuesta de {participant.name}:
"""

            response = self.gemini_model.generate_content(prompt)
            return response.text.strip()

        except Exception as e:
            logger.error(f"Error generating AI response for {participant.name}: {str(e)}")
            raise Exception(f"Error al generar respuesta con Gemini AI para {participant.name}: {str(e)}")

    def _generate_simulated_response(
        self,
        participant: Participant,
        question: str,
        content: str,
        context: Optional[str]
    ) -> str:
        """Generate simulated response as fallback."""
        # Fallback to original template-based approach
        response_styles = {
            "analítica": [
                "Desde mi perspectiva, creo que esto tiene potencial. He notado que los detalles técnicos.",
                "Analizando esto cuidadosamente, me parece interesante. Me preocupa un poco la implementación."
            ],
            "práctico": [
                "Para ser honesto, está bien pero podría ser mejor. Lo que me importa es que funcione bien.",
                "Directamente, me parece interesante. En mi experiencia, que funcione bien."
            ],
            "espontánea": [
                "¡Me encanta! esto tiene potencial. Es súper innovador.",
                "Wow, me parece interesante. Definitivamente lo probaría."
            ],
            "conservador": [
                "Bueno, no me convence del todo. Aunque debo admitir que tengo mis reservas.",
                "Tradicionalmente, esto tiene potencial. Sin embargo, hay que ser cuidadoso."
            ],
            "consciente": [
                "Considerando el impacto, está bien pero podría ser mejor. Es importante que sea sostenible.",
                "Desde una perspectiva responsable, esto tiene potencial. Me gusta que tenga valores claros."
            ]
        }

        primary_trait = participant.personality_traits[0] if participant.personality_traits else "práctico"
        templates = response_styles.get(primary_trait, response_styles["práctico"])
        return random.choice(templates)

    def _generate_opinions_by_profile(self, participant: Participant, content: str) -> List[str]:
        """Generate opinions based on participant profile and content."""

        # Base opinions that can be customized
        base_opinions = [
            "esto tiene potencial",
            "me parece interesante",
            "no me convence del todo",
            "es exactamente lo que buscaba",
            "necesita algunas mejoras",
            "está bien pero podría ser mejor",
            "me genera curiosidad",
            "no es para mí"
        ]

        # Customize based on participant traits
        if "analítica" in participant.personality_traits:
            base_opinions.extend([
                "necesito más datos para decidir",
                "los beneficios no están claros",
                "falta información técnica"
            ])

        if "espontánea" in participant.personality_traits:
            base_opinions.extend([
                "me encanta la idea",
                "suena súper cool",
                "definitivamente lo compartiría"
            ])

        if "conservador" in participant.personality_traits:
            base_opinions.extend([
                "prefiero lo tradicional",
                "no estoy seguro del cambio",
                "necesito más tiempo para pensarlo"
            ])

        return base_opinions

    def _determine_sentiment(self, participant: Participant, content: str, comment: str) -> str:
        """Determine sentiment based on participant profile and comment."""

        # Simple sentiment analysis based on keywords and participant traits
        positive_words = ["encanta", "genial", "excelente", "perfecto", "increíble", "fantástico"]
        negative_words = ["malo", "terrible", "horrible", "odio", "detesto", "pésimo"]

        comment_lower = comment.lower()

        positive_count = sum(1 for word in positive_words if word in comment_lower)
        negative_count = sum(1 for word in negative_words if word in comment_lower)

        # Adjust based on participant traits
        if "espontánea" in participant.personality_traits:
            positive_count += 1  # More likely to be positive
        elif "conservador" in participant.personality_traits:
            negative_count += 1  # More likely to be cautious/negative

        if positive_count > negative_count:
            return "positivo"
        elif negative_count > positive_count:
            return "negativo"
        else:
            return "neutral"

    async def _generate_summary(
        self,
        discussions: List[Discussion],
        participants: List[Participant],
        content: str
    ) -> FocusGroupSummary:
        """Generate comprehensive summary and insights from discussions."""

        # Analyze all comments for insights
        all_comments = []
        for discussion in discussions:
            all_comments.extend(discussion.conversation)

        # Generate key insights
        key_insights = [
            "Los participantes muestran interés general en la propuesta",
            "Se identificaron oportunidades de mejora en la comunicación",
            "La percepción varía según el perfil demográfico",
            "Se requiere mayor claridad en los beneficios",
            "El precio/valor es un factor determinante"
        ]

        # Analyze sentiment
        positive_comments = [c for c in all_comments if c.sentiment == "positivo"]
        negative_comments = [c for c in all_comments if c.sentiment == "negativo"]
        neutral_comments = [c for c in all_comments if c.sentiment == "neutral"]

        overall_sentiment = "neutral"
        if len(positive_comments) > len(negative_comments):
            overall_sentiment = "positivo"
        elif len(negative_comments) > len(positive_comments):
            overall_sentiment = "negativo"

        sentiment_analysis = SentimentAnalysis(
            overall=overall_sentiment,
            breakdown=SentimentBreakdown(
                positive_aspects=[
                    "Innovación y originalidad",
                    "Facilidad de uso percibida",
                    "Potencial de mejora en la vida diaria"
                ],
                negative_aspects=[
                    "Preocupaciones sobre el precio",
                    "Dudas sobre la implementación",
                    "Necesidad de más información"
                ],
                neutral_observations=[
                    "Interés moderado en la categoría",
                    "Comparación con alternativas existentes",
                    "Evaluación de necesidad real"
                ]
            )
        )

        # Generate demographic patterns
        demographic_patterns = [
            DemographicPattern(
                pattern="Los usuarios jóvenes muestran mayor entusiasmo",
                affected_demographics=["18-24", "25-34"]
            ),
            DemographicPattern(
                pattern="Los usuarios con mayor educación requieren más detalles técnicos",
                affected_demographics=["Universitaria", "Posgrado"]
            ),
            DemographicPattern(
                pattern="Los usuarios con ingresos altos son menos sensibles al precio",
                affected_demographics=["Alto", "Medio-alto"]
            )
        ]

        # Generate recommendations
        recommendations = [
            "Mejorar la comunicación de beneficios clave",
            "Desarrollar materiales específicos por segmento demográfico",
            "Considerar estrategias de precios diferenciadas",
            "Ampliar la información técnica disponible",
            "Implementar programa de pruebas gratuitas"
        ]

        return FocusGroupSummary(
            key_insights=key_insights,
            sentiment_analysis=sentiment_analysis,
            demographic_patterns=demographic_patterns,
            recommendations=recommendations
        )

    async def improve_text(self, request: TextAssistRequest) -> TextAssistResponse:
        """
        Improve text content using AI assistance.

        Args:
            request: Text improvement request

        Returns:
            Text improvement suggestions
        """
        try:
            logger.info(f"Improving text: {request.content[:50]}...")

            # Generate improvement suggestions
            if self.gemini_model:
                suggestions = await self._generate_ai_text_suggestions(request.content, request.context)
            else:
                raise Exception("Gemini AI no está disponible. Por favor configura GEMINI_API_KEY para usar el mejorador de texto.")

            return TextAssistResponse(
                status="success",
                suggestions=suggestions
            )

        except Exception as e:
            logger.error(f"Error improving text: {str(e)}")
            return TextAssistResponse(
                status="error",
                error_message=f"Error al mejorar el texto: {str(e)}"
            )

    async def _generate_ai_text_suggestions(self, content: str, context: Optional[str]) -> List[str]:
        """Generate text improvement suggestions using Gemini AI."""
        try:
            # Detectar si es para preparar contenido para focus group
            is_focus_group_prep = context and "contenido principal" in context.lower()

            if is_focus_group_prep:
                prompt = f"""
Eres un experto en marketing que ayuda a las personas a expresar y EXPANDIR mejor sus ideas de productos/servicios para que puedan ser evaluadas por un focus group.

Idea original del usuario: "{content}"

Tu trabajo es tomar esta idea (aunque esté mal redactada o incompleta) y ayudar a expresarla de manera MÁS COMPLETA y detallada para que los participantes del focus group entiendan exactamente qué es, cómo funciona, y qué beneficios ofrece.

INSTRUCCIONES:
- EXPANDE la idea original, no la resumas
- Agrega detalles útiles sobre cómo funcionaría
- Explica los beneficios principales que tendría
- Menciona a quién está dirigido específicamente
- Haz que suene más completa y desarrollada
- Mantén el tono natural y conversacional
- NO uses lenguaje de marketing exagerado
- Ayuda a que la idea se vea más sólida y pensada

Genera 4 versiones EXPANDIDAS de la idea para evaluar en focus group.

FORMATO DE RESPUESTA: Responde SOLO las 4 versiones expandidas, una por línea, sin números, sin explicaciones, sin títulos:
"""
            else:
                prompt = f"""
Eres un copywriter experto que ayuda a las personas a expresar mejor sus ideas. Tu trabajo es tomar lo que alguien quiere decir y mejorarlo manteniendo su esencia y personalidad.

Texto original: "{content}"
{f"Contexto: {context}" if context else ""}

INSTRUCCIONES:
- NO cambies completamente el mensaje, solo mejóralo
- Mantén el tono y la intención original
- Haz que suene más claro, directo y persuasivo
- NO agregues palabras fancy innecesarias
- NO hagas el texto más largo sin razón
- Enfócate en hacer que la idea se entienda mejor

Genera 4 versiones mejoradas que:
1. Sean más claras y directas
2. Tengan mejor impacto emocional
3. Suenen más profesionales pero naturales
4. Mantengan la personalidad del texto original

Responde SOLO las 4 versiones mejoradas, una por línea, sin números ni explicaciones:
"""

            response = self.gemini_model.generate_content(prompt)
            suggestions_text = response.text.strip()

            # Parse suggestions - split by lines and clean up
            suggestions = []
            lines = suggestions_text.split('\n')

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Remove common prefixes like numbers, bullets, etc.
                if line.startswith(('1.', '2.', '3.', '4.', '•', '-', '*')):
                    line = line[2:].strip()
                elif line.startswith(('1)', '2)', '3)', '4)')):
                    line = line[2:].strip()

                # Only add non-empty, meaningful suggestions
                if line and len(line) > 10:  # Avoid very short responses
                    suggestions.append(line)

            # If no good suggestions found, try splitting by other methods
            if not suggestions:
                # Try splitting by double newlines or periods
                potential_suggestions = suggestions_text.replace('\n\n', '|').split('|')
                for suggestion in potential_suggestions:
                    suggestion = suggestion.strip()
                    if suggestion and len(suggestion) > 10:
                        suggestions.append(suggestion)

            # Last resort: return the whole response
            if not suggestions:
                suggestions = [suggestions_text]

            return suggestions[:4]  # Ensure max 4 suggestions

        except Exception as e:
            logger.error(f"Error generating AI text suggestions: {str(e)}")
            raise Exception(f"Error al generar sugerencias con Gemini AI: {str(e)}")

    def _generate_text_suggestions(self, content: str, context: Optional[str]) -> List[str]:
        """Generate text improvement suggestions."""

        # Base improvements (in a real implementation, this would use LLM)
        suggestions = []

        # Length-based suggestions
        if len(content) < 50:
            suggestions.append(f"{content} - Versión expandida con más detalles y beneficios específicos.")

        # Context-based suggestions
        if context and "contenido principal" in context:
            suggestions.extend([
                f"{content} - Optimizado para mayor impacto y claridad.",
                f"Descubre {content.lower()} - Una propuesta que transformará tu experiencia.",
                f"{content} - La solución innovadora que estabas buscando."
            ])
        elif context and "pregunta personalizada" in context:
            suggestions.extend([
                f"¿{content}?" if not content.endswith("?") else content,
                f"En tu opinión, {content.lower()}",
                f"¿Cómo percibes {content.lower()}?"
            ])
        else:
            suggestions.extend([
                f"{content} - Versión mejorada con mayor claridad.",
                f"{content} - Optimizado para mejor comprensión.",
                f"{content} - Refinado para mayor impacto."
            ])

        return suggestions[:5]  # Return max 5 suggestions


# Create service instance
focus_group_service = FocusGroupService()

"""Service for geographic and cultural analysis of buyer personas."""

import logging
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
import google.generativeai as genai
from app.core.config import settings

# Try to import pytz, fallback if not available
try:
    import pytz
    PYTZ_AVAILABLE = True
except ImportError:
    PYTZ_AVAILABLE = False

logger = logging.getLogger(__name__)


class GeographicAnalysisService:
    """Service for analyzing geographic and cultural factors affecting buyer personas."""

    def __init__(self):
        """Initialize the geographic analysis service."""
        self.gemini_model = None
        if settings.GEMINI_API_KEY:
            try:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("Gemini AI initialized successfully for geographic analysis")
            except Exception as e:
                logger.error(f"Failed to initialize Gemini AI: {e}")
                self.gemini_model = None
        else:
            logger.warning("GEMINI_API_KEY not found. Geographic analysis will use static data.")

    async def analyze_geographic_factors(
        self,
        persona_data: Dict[str, Any],
        target_regions: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Analyze geographic and cultural factors for buyer personas.

        Args:
            persona_data: Complete buyer persona information
            target_regions: Optional list of target regions/countries

        Returns:
            Comprehensive geographic and cultural analysis
        """
        try:
            # Extract location from persona
            location = persona_data.get('location', 'Global')

            # If no target regions specified, analyze persona's current location
            if not target_regions:
                target_regions = [self._extract_country_from_location(location)]

            analysis_results = {}

            for region in target_regions:
                region_analysis = await self._analyze_region(persona_data, region)
                analysis_results[region] = region_analysis

            # Generate comparative analysis if multiple regions
            comparative_analysis = None
            if len(target_regions) > 1:
                comparative_analysis = await self._generate_comparative_analysis(
                    persona_data, analysis_results
                )

            return {
                "status": "success",
                "persona_location": location,
                "analyzed_regions": target_regions,
                "regional_analysis": analysis_results,
                "comparative_analysis": comparative_analysis,
                "recommendations": await self._generate_regional_recommendations(
                    persona_data, analysis_results
                ),
                "generated_at": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in geographic analysis: {e}")
            return {
                "status": "error",
                "error_message": f"Failed to analyze geographic factors: {str(e)}"
            }

    async def _analyze_region(
        self,
        persona_data: Dict[str, Any],
        region: str
    ) -> Dict[str, Any]:
        """Analyze a specific region for the persona."""

        # Get cultural context
        cultural_context = self._get_cultural_context(region)

        # Get business context
        business_context = self._get_business_context(region)

        # Get timezone and optimal timing
        timezone_info = self._get_timezone_info(region)

        # Get communication preferences
        communication_prefs = self._get_communication_preferences(region)

        # Get compliance requirements
        compliance_info = self._get_compliance_requirements(region)

        # Generate AI-enhanced analysis if available
        if self.gemini_model:
            enhanced_analysis = await self._generate_ai_regional_analysis(
                persona_data, region, cultural_context, business_context
            )
        else:
            enhanced_analysis = {}

        return {
            "region": region,
            "cultural_context": cultural_context,
            "business_context": business_context,
            "timezone_info": timezone_info,
            "communication_preferences": communication_prefs,
            "compliance_requirements": compliance_info,
            "ai_insights": enhanced_analysis,
            "localization_score": self._calculate_localization_score(
                persona_data, cultural_context, business_context
            )
        }

    def _extract_country_from_location(self, location: str) -> str:
        """Extract country from location string."""
        location_lower = location.lower()

        # Common country mappings
        country_mappings = {
            'madrid': 'España',
            'barcelona': 'España',
            'mexico': 'México',
            'cdmx': 'México',
            'bogota': 'Colombia',
            'lima': 'Perú',
            'santiago': 'Chile',
            'buenos aires': 'Argentina',
            'new york': 'Estados Unidos',
            'california': 'Estados Unidos',
            'london': 'Reino Unido',
            'paris': 'Francia',
            'berlin': 'Alemania',
            'tokyo': 'Japón',
            'singapore': 'Singapur'
        }

        for city, country in country_mappings.items():
            if city in location_lower:
                return country

        # If no match found, return the location as is
        return location

    def _get_cultural_context(self, region: str) -> Dict[str, Any]:
        """Get cultural context for a region."""

        cultural_data = {
            'España': {
                'language': 'Español',
                'business_culture': 'Formal pero personal',
                'communication_style': 'Directo pero cortés',
                'relationship_importance': 'Alta',
                'hierarchy_respect': 'Moderado',
                'time_orientation': 'Flexible',
                'decision_making': 'Consensual',
                'trust_building': 'Relaciones personales importantes',
                'negotiation_style': 'Colaborativo',
                'cultural_values': ['Familia', 'Respeto', 'Tradición', 'Innovación'],
                'business_etiquette': [
                    'Saludo con apretón de manos',
                    'Uso de títulos formales inicialmente',
                    'Importancia de las comidas de negocios',
                    'Puntualidad valorada pero flexible'
                ]
            },
            'México': {
                'language': 'Español',
                'business_culture': 'Jerárquico y personal',
                'communication_style': 'Indirecto y cortés',
                'relationship_importance': 'Muy alta',
                'hierarchy_respect': 'Alto',
                'time_orientation': 'Flexible',
                'decision_making': 'Jerárquico',
                'trust_building': 'Relaciones familiares y personales',
                'negotiation_style': 'Relacional',
                'cultural_values': ['Familia', 'Respeto', 'Hospitalidad', 'Tradición'],
                'business_etiquette': [
                    'Saludo cálido y personal',
                    'Importancia de la jerarquía',
                    'Tiempo para relaciones personales',
                    'Respeto por la edad y experiencia'
                ]
            },
            'Estados Unidos': {
                'language': 'Inglés',
                'business_culture': 'Directo y eficiente',
                'communication_style': 'Directo y específico',
                'relationship_importance': 'Moderada',
                'hierarchy_respect': 'Bajo',
                'time_orientation': 'Estricto',
                'decision_making': 'Individual/Rápido',
                'trust_building': 'Competencia y resultados',
                'negotiation_style': 'Competitivo',
                'cultural_values': ['Individualismo', 'Eficiencia', 'Innovación', 'Competencia'],
                'business_etiquette': [
                    'Apretón de manos firme',
                    'Puntualidad estricta',
                    'Comunicación directa',
                    'Enfoque en resultados'
                ]
            },
            'Reino Unido': {
                'language': 'Inglés',
                'business_culture': 'Formal y tradicional',
                'communication_style': 'Indirecto y diplomático',
                'relationship_importance': 'Moderada',
                'hierarchy_respect': 'Moderado',
                'time_orientation': 'Puntual',
                'decision_making': 'Consensual',
                'trust_building': 'Profesionalismo y cortesía',
                'negotiation_style': 'Diplomático',
                'cultural_values': ['Cortesía', 'Tradición', 'Understatement', 'Fair play'],
                'business_etiquette': [
                    'Cortesía extrema',
                    'Puntualidad importante',
                    'Conversación educada',
                    'Respeto por las colas'
                ]
            }
        }

        return cultural_data.get(region, {
            'language': 'Local',
            'business_culture': 'Varies by region',
            'communication_style': 'Professional',
            'relationship_importance': 'Moderate',
            'hierarchy_respect': 'Moderate',
            'time_orientation': 'Punctual',
            'decision_making': 'Collaborative',
            'trust_building': 'Professional competence',
            'negotiation_style': 'Balanced',
            'cultural_values': ['Professionalism', 'Respect', 'Quality'],
            'business_etiquette': ['Professional greeting', 'Punctuality', 'Respectful communication']
        })

    def _get_business_context(self, region: str) -> Dict[str, Any]:
        """Get business context for a region."""

        business_data = {
            'España': {
                'business_hours': '9:00-18:00',
                'lunch_break': '14:00-15:00',
                'preferred_meeting_times': ['10:00-12:00', '16:00-17:00'],
                'vacation_periods': ['Julio-Agosto', 'Navidad'],
                'payment_methods': ['Transferencia bancaria', 'Tarjeta', 'Efectivo'],
                'business_registration': 'Registro Mercantil',
                'tax_considerations': 'IVA 21%',
                'common_industries': ['Turismo', 'Tecnología', 'Manufactura', 'Servicios'],
                'economic_indicators': {
                    'gdp_per_capita': '€27,000',
                    'business_ease_rank': 30,
                    'digital_adoption': 'Alta'
                }
            },
            'México': {
                'business_hours': '9:00-18:00',
                'lunch_break': '14:00-15:00',
                'preferred_meeting_times': ['10:00-12:00', '15:00-17:00'],
                'vacation_periods': ['Diciembre', 'Semana Santa'],
                'payment_methods': ['Transferencia', 'Tarjeta', 'Efectivo'],
                'business_registration': 'RFC y SAT',
                'tax_considerations': 'IVA 16%',
                'common_industries': ['Manufactura', 'Servicios', 'Tecnología', 'Turismo'],
                'economic_indicators': {
                    'gdp_per_capita': '$9,500',
                    'business_ease_rank': 60,
                    'digital_adoption': 'Media-Alta'
                }
            },
            'Estados Unidos': {
                'business_hours': '9:00-17:00',
                'lunch_break': '12:00-13:00',
                'preferred_meeting_times': ['9:00-11:00', '14:00-16:00'],
                'vacation_periods': ['Julio 4', 'Thanksgiving', 'Navidad'],
                'payment_methods': ['Tarjeta', 'ACH', 'Cheque'],
                'business_registration': 'State incorporation',
                'tax_considerations': 'Sales tax varies by state',
                'common_industries': ['Tecnología', 'Finanzas', 'Salud', 'Manufactura'],
                'economic_indicators': {
                    'gdp_per_capita': '$65,000',
                    'business_ease_rank': 6,
                    'digital_adoption': 'Muy Alta'
                }
            }
        }

        return business_data.get(region, {
            'business_hours': '9:00-17:00',
            'lunch_break': '12:00-13:00',
            'preferred_meeting_times': ['10:00-12:00', '14:00-16:00'],
            'vacation_periods': ['Local holidays'],
            'payment_methods': ['Bank transfer', 'Credit card'],
            'business_registration': 'Local requirements',
            'tax_considerations': 'Local tax rates',
            'common_industries': ['Services', 'Technology', 'Manufacturing'],
            'economic_indicators': {
                'gdp_per_capita': 'Varies',
                'business_ease_rank': 'N/A',
                'digital_adoption': 'Medium'
            }
        })

    def _get_timezone_info(self, region: str) -> Dict[str, Any]:
        """Get timezone and optimal timing information."""

        timezone_data = {
            'España': {
                'timezone': 'Europe/Madrid',
                'utc_offset': '+1/+2',
                'optimal_contact_hours': ['9:00-12:00', '15:00-18:00'],
                'optimal_days': ['Martes', 'Miércoles', 'Jueves'],
                'avoid_times': ['14:00-15:00 (almuerzo)', '18:00+ (fin de jornada)'],
                'seasonal_considerations': 'Agosto es período vacacional'
            },
            'México': {
                'timezone': 'America/Mexico_City',
                'utc_offset': '-6',
                'optimal_contact_hours': ['10:00-12:00', '15:00-17:00'],
                'optimal_days': ['Martes', 'Miércoles', 'Jueves'],
                'avoid_times': ['14:00-15:00 (comida)', 'Viernes tarde'],
                'seasonal_considerations': 'Diciembre y Semana Santa son períodos vacacionales'
            },
            'Estados Unidos': {
                'timezone': 'Multiple (EST/CST/MST/PST)',
                'utc_offset': '-5 to -8',
                'optimal_contact_hours': ['9:00-11:00', '14:00-16:00'],
                'optimal_days': ['Martes', 'Miércoles', 'Jueves'],
                'avoid_times': ['Lunes temprano', 'Viernes tarde'],
                'seasonal_considerations': 'Thanksgiving week, Christmas week'
            }
        }

        return timezone_data.get(region, {
            'timezone': 'Local timezone',
            'utc_offset': 'Varies',
            'optimal_contact_hours': ['9:00-12:00', '14:00-17:00'],
            'optimal_days': ['Tuesday', 'Wednesday', 'Thursday'],
            'avoid_times': ['Lunch hours', 'End of day'],
            'seasonal_considerations': 'Local holidays and vacation periods'
        })

    def _get_communication_preferences(self, region: str) -> Dict[str, Any]:
        """Get communication preferences by region."""

        comm_data = {
            'España': {
                'preferred_channels': ['Email', 'WhatsApp Business', 'LinkedIn', 'Teléfono'],
                'channel_effectiveness': {
                    'Email': 85,
                    'WhatsApp Business': 90,
                    'LinkedIn': 75,
                    'Teléfono': 80,
                    'SMS': 60
                },
                'response_expectations': '24-48 horas',
                'formality_level': 'Formal inicial, personal después',
                'language_preferences': ['Español', 'Inglés (negocios)'],
                'cultural_communication_notes': [
                    'Importancia del saludo personal',
                    'Uso de "usted" inicialmente',
                    'Apreciación por el contexto cultural'
                ]
            },
            'México': {
                'preferred_channels': ['WhatsApp Business', 'Email', 'Teléfono', 'LinkedIn'],
                'channel_effectiveness': {
                    'WhatsApp Business': 95,
                    'Email': 80,
                    'Teléfono': 85,
                    'LinkedIn': 70,
                    'SMS': 75
                },
                'response_expectations': '24-72 horas',
                'formality_level': 'Muy formal y respetuoso',
                'language_preferences': ['Español', 'Inglés (limitado)'],
                'cultural_communication_notes': [
                    'Importancia de la cortesía extrema',
                    'Uso de títulos y jerarquía',
                    'Tiempo para relaciones personales'
                ]
            },
            'Estados Unidos': {
                'preferred_channels': ['Email', 'LinkedIn', 'Teléfono', 'Slack'],
                'channel_effectiveness': {
                    'Email': 90,
                    'LinkedIn': 85,
                    'Teléfono': 75,
                    'Slack': 80,
                    'SMS': 50
                },
                'response_expectations': '24 horas',
                'formality_level': 'Profesional pero directo',
                'language_preferences': ['Inglés'],
                'cultural_communication_notes': [
                    'Comunicación directa y eficiente',
                    'Enfoque en resultados',
                    'Puntualidad en respuestas'
                ]
            }
        }

        return comm_data.get(region, {
            'preferred_channels': ['Email', 'Phone', 'LinkedIn'],
            'channel_effectiveness': {
                'Email': 80,
                'Phone': 75,
                'LinkedIn': 70
            },
            'response_expectations': '24-48 hours',
            'formality_level': 'Professional',
            'language_preferences': ['Local language', 'English'],
            'cultural_communication_notes': ['Professional communication', 'Respectful tone']
        })

    def _get_compliance_requirements(self, region: str) -> Dict[str, Any]:
        """Get compliance and legal requirements by region."""

        compliance_data = {
            'España': {
                'data_protection': 'GDPR',
                'privacy_requirements': [
                    'Consentimiento explícito para marketing',
                    'Derecho al olvido',
                    'Portabilidad de datos',
                    'Notificación de brechas en 72h'
                ],
                'marketing_regulations': [
                    'Ley de Servicios de la Sociedad de la Información',
                    'Prohibición de spam sin consentimiento',
                    'Identificación clara del remitente'
                ],
                'business_regulations': [
                    'Registro en AEPD para tratamiento de datos',
                    'Facturación electrónica obligatoria',
                    'IVA 21% en servicios digitales'
                ],
                'required_disclaimers': [
                    'Política de privacidad',
                    'Términos y condiciones',
                    'Información de cookies'
                ]
            },
            'México': {
                'data_protection': 'LFPDPPP',
                'privacy_requirements': [
                    'Aviso de privacidad obligatorio',
                    'Consentimiento para datos sensibles',
                    'Derechos ARCO (Acceso, Rectificación, Cancelación, Oposición)'
                ],
                'marketing_regulations': [
                    'PROFECO para publicidad',
                    'Prohibición de publicidad engañosa',
                    'Registro ante CONDUSEF para servicios financieros'
                ],
                'business_regulations': [
                    'RFC obligatorio',
                    'Facturación electrónica (CFDI)',
                    'IVA 16% en servicios'
                ],
                'required_disclaimers': [
                    'Aviso de privacidad',
                    'Términos y condiciones',
                    'Política de cookies'
                ]
            },
            'Estados Unidos': {
                'data_protection': 'CCPA/CPRA (California), State-specific',
                'privacy_requirements': [
                    'Privacy policy required',
                    'Opt-out rights (California)',
                    'Data breach notification laws',
                    'CAN-SPAM Act compliance'
                ],
                'marketing_regulations': [
                    'CAN-SPAM Act',
                    'TCPA for phone/SMS marketing',
                    'FTC advertising guidelines'
                ],
                'business_regulations': [
                    'State business registration',
                    'Sales tax collection (varies by state)',
                    'Industry-specific regulations'
                ],
                'required_disclaimers': [
                    'Privacy policy',
                    'Terms of service',
                    'Cookie policy',
                    'Accessibility statement'
                ]
            }
        }

        return compliance_data.get(region, {
            'data_protection': 'Local data protection laws',
            'privacy_requirements': ['Privacy policy', 'Consent management'],
            'marketing_regulations': ['Anti-spam laws', 'Advertising standards'],
            'business_regulations': ['Business registration', 'Tax compliance'],
            'required_disclaimers': ['Privacy policy', 'Terms of service']
        })

    def _calculate_localization_score(
        self,
        persona_data: Dict[str, Any],
        cultural_context: Dict[str, Any],
        business_context: Dict[str, Any]
    ) -> int:
        """Calculate how well the persona fits the regional context."""

        score = 50  # Base score

        # Adjust based on cultural alignment
        persona_location = persona_data.get('location', '')
        if any(country in persona_location for country in ['España', 'Mexico', 'Estados Unidos']):
            score += 20

        # Adjust based on communication style match
        persona_channels = persona_data.get('communication_channels', [])
        preferred_channels = cultural_context.get('business_etiquette', [])
        if any(channel in str(preferred_channels) for channel in persona_channels):
            score += 15

        # Adjust based on business context
        persona_industry = persona_data.get('job', {}).get('industry', '')
        common_industries = business_context.get('common_industries', [])
        if any(industry.lower() in persona_industry.lower() for industry in common_industries):
            score += 15

        return min(max(score, 0), 100)

    async def _generate_ai_regional_analysis(
        self,
        persona_data: Dict[str, Any],
        region: str,
        cultural_context: Dict[str, Any],
        business_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate AI-enhanced regional analysis."""

        if not self.gemini_model:
            return {}

        prompt = f"""
        Analiza las consideraciones culturales y de negocio para esta buyer persona en {region}:

        PERSONA:
        - Nombre: {persona_data.get('name', 'Unknown')}
        - Cargo: {persona_data.get('job', {}).get('title', 'Professional')}
        - Industria: {persona_data.get('job', {}).get('industry', 'Technology')}
        - Objetivos: {', '.join(persona_data.get('goals', []))}
        - Desafíos: {', '.join(persona_data.get('challenges', []))}

        CONTEXTO CULTURAL DE {region}:
        - Estilo de comunicación: {cultural_context.get('communication_style', 'Professional')}
        - Importancia de relaciones: {cultural_context.get('relationship_importance', 'Medium')}
        - Toma de decisiones: {cultural_context.get('decision_making', 'Collaborative')}

        Proporciona insights específicos sobre:
        1. Adaptaciones culturales necesarias
        2. Estrategias de comunicación específicas
        3. Consideraciones de timing y estacionalidad
        4. Riesgos culturales a evitar
        5. Oportunidades específicas de la región

        Responde en formato JSON con estas claves: cultural_adaptations, communication_strategies, timing_considerations, cultural_risks, regional_opportunities
        """

        try:
            response = self.gemini_model.generate_content(prompt)
            # Parse and return AI insights
            return {"ai_generated_insights": response.text[:500]}  # Truncate for now
        except Exception as e:
            logger.error(f"Error generating AI regional analysis: {e}")
            return {}

    async def _generate_comparative_analysis(
        self,
        persona_data: Dict[str, Any],
        regional_analyses: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate comparative analysis across regions."""

        regions = list(regional_analyses.keys())

        # Compare localization scores
        scores = {region: analysis['localization_score']
                 for region, analysis in regional_analyses.items()}

        best_region = max(scores, key=scores.get)

        # Compare communication preferences
        comm_comparison = {}
        for region, analysis in regional_analyses.items():
            comm_prefs = analysis['communication_preferences']
            comm_comparison[region] = {
                'top_channel': max(comm_prefs['channel_effectiveness'],
                                 key=comm_prefs['channel_effectiveness'].get),
                'response_time': comm_prefs['response_expectations'],
                'formality': comm_prefs['formality_level']
            }

        return {
            'recommended_region': best_region,
            'localization_scores': scores,
            'communication_comparison': comm_comparison,
            'key_differences': [
                'Estilos de comunicación varían significativamente',
                'Horarios óptimos de contacto difieren por zona horaria',
                'Requisitos de compliance específicos por región',
                'Preferencias de canal de comunicación regionales'
            ],
            'expansion_strategy': f'Comenzar con {best_region} y expandir gradualmente'
        }

    async def _generate_regional_recommendations(
        self,
        persona_data: Dict[str, Any],
        regional_analyses: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate actionable recommendations for each region."""

        recommendations = []

        for region, analysis in regional_analyses.items():
            region_recs = {
                'region': region,
                'priority': 'High' if analysis['localization_score'] > 70 else 'Medium' if analysis['localization_score'] > 50 else 'Low',
                'quick_wins': [
                    f"Usar {analysis['communication_preferences']['preferred_channels'][0]} como canal principal",
                    f"Contactar durante {analysis['timezone_info']['optimal_contact_hours'][0]}",
                    f"Adaptar tono a: {analysis['cultural_context']['communication_style']}"
                ],
                'implementation_steps': [
                    'Localizar contenido de marketing',
                    'Configurar horarios de contacto regionales',
                    'Implementar compliance local',
                    'Entrenar equipo en diferencias culturales'
                ],
                'success_metrics': [
                    'Tasa de respuesta por canal',
                    'Tiempo de conversión',
                    'Satisfacción cultural del cliente',
                    'Compliance score'
                ]
            }
            recommendations.append(region_recs)

        return recommendations


# Create singleton instance
geographic_analysis_service = GeographicAnalysisService()

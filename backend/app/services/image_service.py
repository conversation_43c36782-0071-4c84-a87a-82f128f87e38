"""Service for image generation and manipulation using Stability AI v2beta API."""

import logging
from typing import Dict
import httpx
from fastapi import HTTPException

from app.core.config import settings
from app.schemas.image import ImageGenerationRequest, ImageGenerationResponse

logger = logging.getLogger(__name__)

def get_stability_headers_v2beta() -> Dict[str, str]:
    """Get headers for Stability AI v2beta API requests."""
    return {
        "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
        "Accept": "image/*"  # Para recibir imagen directamente
    }

def get_stability_headers_json() -> Dict[str, str]:
    """Get headers for Stability AI v2beta API requests (JSON response)."""
    return {
        "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
        "Accept": "application/json"  # Para recibir respuesta JSON con base64
    }

def convert_aspect_ratio(width: int, height: int) -> str:
    """Convert width/height to aspect ratio string."""
    # Calcular ratio y mapear a los valores soportados
    ratio = width / height

    if abs(ratio - 16/9) < 0.1:
        return "16:9"
    elif abs(ratio - 21/9) < 0.1:
        return "21:9"
    elif abs(ratio - 2/3) < 0.1:
        return "2:3"
    elif abs(ratio - 3/2) < 0.1:
        return "3:2"
    elif abs(ratio - 4/5) < 0.1:
        return "4:5"
    elif abs(ratio - 5/4) < 0.1:
        return "5:4"
    elif abs(ratio - 9/16) < 0.1:
        return "9:16"
    elif abs(ratio - 9/21) < 0.1:
        return "9:21"
    else:
        return "1:1"  # Default cuadrado

async def generate_image_ultra(request: ImageGenerationRequest) -> ImageGenerationResponse:
    """Generate an image using Stability AI Ultra (v2beta) - Highest quality."""
    try:
        # Traducir prompt al inglés (Stability AI solo acepta inglés)
        translated_prompt = translate_prompt_to_english(request.prompt)

        # Preparar FormData para la API v2beta
        form_data = {
            "prompt": translated_prompt,
            "output_format": "webp",  # Formato optimizado
            "aspect_ratio": convert_aspect_ratio(request.width, request.height)
        }

        # Agregar negative_prompt si está disponible
        if hasattr(request, 'negative_prompt') and request.negative_prompt:
            translated_negative_prompt = translate_prompt_to_english(request.negative_prompt)
            form_data["negative_prompt"] = translated_negative_prompt

        # Agregar style_preset si está disponible
        if hasattr(request, 'style_preset') and request.style_preset:
            form_data["style_preset"] = request.style_preset

        # Agregar seed si está disponible
        if hasattr(request, 'seed') and request.seed:
            form_data["seed"] = request.seed

        # URL de la API v2beta Ultra
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/generate/ultra"
        headers = get_stability_headers_json()

        logger.info("Making request to Stability AI Ultra v2beta with prompt: %s", request.prompt[:50])

        # Usar files={"none": ""} según documentación oficial de Stability AI
        files = {"none": ""}

        async with httpx.AsyncClient(timeout=180.0) as client:  # Aumentar a 3 minutos
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )
            response.raise_for_status()

            # Con Accept: application/json, siempre recibimos JSON con base64
            result = response.json()
            image_data = result.get("image")
            if not image_data:
                raise ValueError("No image data in JSON response")

            return ImageGenerationResponse(
                image=image_data,
                metadata={
                    "engine": "stable-image-ultra",
                    "prompt": request.prompt,
                    "finish_reason": result.get("finish_reason", "SUCCESS"),
                    "seed": result.get("seed")
                },
                error=None
            )

    except httpx.RequestError as e:
        logger.error("Error making request to Stability AI Ultra: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Error generating image with Ultra: {str(e)}"
        ) from e
    except Exception as e:
        logger.error("Unexpected error generating image with Ultra: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        ) from e

async def generate_image_core(request: ImageGenerationRequest) -> ImageGenerationResponse:
    """Generate an image using Stability AI Core (v2beta) - Fast and affordable."""
    try:
        # Traducir prompt al inglés (Stability AI solo acepta inglés)
        translated_prompt = translate_prompt_to_english(request.prompt)

        # Preparar FormData para la API v2beta Core
        form_data = {
            "prompt": translated_prompt,
            "output_format": "webp",
            "aspect_ratio": convert_aspect_ratio(request.width, request.height)
        }

        # Agregar campos opcionales
        if hasattr(request, 'negative_prompt') and request.negative_prompt:
            translated_negative_prompt = translate_prompt_to_english(request.negative_prompt)
            form_data["negative_prompt"] = translated_negative_prompt
        if hasattr(request, 'style_preset') and request.style_preset:
            form_data["style_preset"] = request.style_preset
        if hasattr(request, 'seed') and request.seed:
            form_data["seed"] = request.seed

        # URL de la API v2beta Core
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/generate/core"
        headers = get_stability_headers_json()

        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        logger.info(f"Using Stability API key: {settings.STABILITY_API_KEY[:10]}...")
        logger.info("Making request to Stability AI Core v2beta with prompt: %s", request.prompt[:50])
        logger.info(f"URL: {url}")
        logger.info(f"Headers: {headers}")
        logger.info(f"Form data: {form_data}")

        # Usar files={"none": ""} según documentación oficial de Stability AI
        files = {"none": ""}

        async with httpx.AsyncClient(timeout=180.0) as client:  # Aumentar a 3 minutos
            response = await client.post(url, headers=headers, data=form_data, files=files)
            response.raise_for_status()

            # Con Accept: application/json, siempre recibimos JSON con base64
            result = response.json()
            image_data = result.get("image")
            if not image_data:
                raise ValueError("No image data in JSON response")

            return ImageGenerationResponse(
                image=image_data,
                metadata={
                    "engine": "stable-image-core",
                    "prompt": request.prompt,
                    "finish_reason": result.get("finish_reason", "SUCCESS"),
                    "seed": result.get("seed")
                },
                error=None
            )

    except httpx.RequestError as e:
        logger.error("Error making request to Stability AI Core: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Error generating image with Core: {str(e)}"
        ) from e
    except Exception as e:
        logger.error("Unexpected error generating image with Core: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        ) from e

async def generate_image_sd3(request: ImageGenerationRequest) -> ImageGenerationResponse:
    """Generate an image using Stable Diffusion 3.5 (v2beta) - Latest base models."""
    try:
        # Traducir prompt al inglés (Stability AI solo acepta inglés)
        translated_prompt = translate_prompt_to_english(request.prompt)

        # Preparar FormData para SD3
        # IMPORTANTE: SD3 NO soporta webp, solo jpeg y png
        output_format = "png"  # SD3 solo soporta jpeg y png
        form_data = {
            "prompt": translated_prompt,
            "output_format": output_format,
            "aspect_ratio": convert_aspect_ratio(request.width, request.height),
            "model": "sd3.5-large-turbo"  # Usar el modelo turbo más rápido (4 créditos)
        }

        # Agregar campos opcionales
        if hasattr(request, 'negative_prompt') and request.negative_prompt:
            translated_negative_prompt = translate_prompt_to_english(request.negative_prompt)
            form_data["negative_prompt"] = translated_negative_prompt
        if hasattr(request, 'seed') and request.seed:
            form_data["seed"] = request.seed

        # URL de la API v2beta SD3
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/generate/sd3"
        headers = get_stability_headers_json()

        logger.info("Making request to Stability AI SD3.5 v2beta with prompt: %s", request.prompt[:50])

        # Usar files={"none": ""} según documentación oficial de Stability AI
        files = {"none": ""}

        async with httpx.AsyncClient(timeout=180.0) as client:  # Aumentar a 3 minutos
            response = await client.post(url, headers=headers, data=form_data, files=files)
            response.raise_for_status()

            # Con Accept: application/json, siempre recibimos JSON con base64
            result = response.json()
            image_data = result.get("image")
            if not image_data:
                raise ValueError("No image data in JSON response")

            return ImageGenerationResponse(
                image=image_data,
                metadata={
                    "engine": "stable-diffusion-3.5",
                    "prompt": request.prompt,
                    "finish_reason": result.get("finish_reason", "SUCCESS"),
                    "seed": result.get("seed")
                },
                error=None
            )

    except httpx.RequestError as e:
        logger.error("Error making request to Stability AI SD3: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Error generating image with SD3: {str(e)}"
        ) from e
    except Exception as e:
        logger.error("Unexpected error generating image with SD3: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        ) from e

# Función principal que selecciona el mejor modelo según el contexto
async def generate_image(request: ImageGenerationRequest) -> ImageGenerationResponse:
    """
    Generate an image using the best Stability AI model based on the request.

    Selection logic:
    - Ultra: For high-quality, professional use cases
    - Core: For fast iteration and development
    - SD3: For latest model features and balanced performance
    """
    try:
        # Determinar el modelo a usar basado en el contexto
        # Por defecto, usar Ultra para la mejor calidad
        model_preference = getattr(request, 'model', 'ultra')

        if model_preference == 'core':
            return await generate_image_core(request)
        elif model_preference == 'sd3':
            return await generate_image_sd3(request)
        else:
            # Default a Ultra para la mejor calidad
            return await generate_image_ultra(request)

    except Exception as e:
        logger.error("Error in main generate_image function: %s", str(e))
        # Fallback a Core si Ultra falla
        if model_preference != 'core':
            logger.info("Falling back to Core model...")
            try:
                return await generate_image_core(request)
            except Exception as fallback_error:
                logger.error("Fallback to Core also failed: %s", str(fallback_error))

        raise HTTPException(
            status_code=500,
            detail=f"All image generation methods failed: {str(e)}"
        )


def translate_prompt_to_english(spanish_prompt: str) -> str:
    """
    Traduce prompts del español al inglés para Stability AI.
    Stability AI solo acepta prompts en inglés.
    """
    if not spanish_prompt or spanish_prompt.strip() == '':
        return spanish_prompt

    # Diccionario básico de traducciones comunes para prompts de IA
    translations = {
        # Personas y características
        'mujer': 'woman',
        'hombre': 'man',
        'niño': 'child',
        'niña': 'girl',
        'persona': 'person',
        'retrato': 'portrait',
        'cara': 'face',
        'cabello': 'hair',
        'ojos': 'eyes',
        'sonrisa': 'smile',
        'joven': 'young',
        'viejo': 'old',
        'hermoso': 'beautiful',
        'hermosa': 'beautiful',
        'elegante': 'elegant',

        # Lugares y ambientes
        'playa': 'beach',
        'montaña': 'mountain',
        'bosque': 'forest',
        'ciudad': 'city',
        'campo': 'countryside',
        'jardín': 'garden',
        'parque': 'park',
        'casa': 'house',
        'oficina': 'office',
        'estudio': 'studio',
        'calle': 'street',
        'noche': 'night',
        'día': 'day',

        # Colores
        'rojo': 'red',
        'azul': 'blue',
        'verde': 'green',
        'amarillo': 'yellow',
        'negro': 'black',
        'blanco': 'white',
        'gris': 'gray',
        'rosa': 'pink',
        'morado': 'purple',
        'naranja': 'orange',
        'marrón': 'brown',

        # Estilos artísticos
        'steampunk': 'steampunk',
        'cyberpunk': 'cyberpunk',
        'vintage': 'vintage',
        'moderno': 'modern',
        'clásico': 'classic',
        'artístico': 'artistic',
        'realista': 'realistic',
        'abstracto': 'abstract',
        'minimalista': 'minimalist',

        # Palabras de conexión
        'con': 'with',
        'sin': 'without',
        'en': 'in',
        'de': 'of',
        'del': 'of the',
        'la': 'the',
        'el': 'the',
        'una': 'a',
        'un': 'a',
        'y': 'and',
        'o': 'or',
        'pero': 'but',
        'estilo': 'style',
        'imagen': 'image',
        'dormir': 'sleep',
        'bien': 'well',
        'salud': 'health',
        'importante': 'important',
        'fundamental': 'fundamental',
        'descanso': 'rest',
        'sueño': 'sleep',
        'calidad': 'quality',
        'relajante': 'relaxing',
        'rutina': 'routine',
        'horas': 'hours',
        'diarias': 'daily',
        'contenido': 'content',
        'post': 'post',
        'fotografía': 'photography',
        'profesional': 'professional',
        'iluminación': 'lighting',
        'composición': 'composition',
        'conceptos': 'concepts',
        'clave': 'key',
        'incluye': 'includes',
        'estos': 'these',
        'sobre': 'about',
        'importancia': 'importance',
    }

    translated_prompt = spanish_prompt.lower()

    # Aplicar traducciones palabra por palabra
    import re
    for spanish_word, english_word in translations.items():
        # Usar word boundaries para evitar traducciones parciales
        pattern = r'\b' + re.escape(spanish_word) + r'\b'
        translated_prompt = re.sub(pattern, english_word, translated_prompt, flags=re.IGNORECASE)

    logger.info(f"🌐 Prompt traducido: '{spanish_prompt}' → '{translated_prompt}'")
    return translated_prompt

"""Service for predicting buyer behavior and conversion patterns."""

import logging
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import google.generativeai as genai
from app.core.config import settings

logger = logging.getLogger(__name__)


class BehaviorPredictionService:
    """Service for predicting buyer behavior and conversion patterns."""

    def __init__(self):
        """Initialize the behavior prediction service."""
        self.gemini_model = None
        if settings.GEMINI_API_KEY:
            try:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("Gemini AI initialized successfully for behavior prediction")
            except Exception as e:
                logger.error(f"Failed to initialize Gemini AI: {e}")
                self.gemini_model = None
        else:
            logger.warning("GEMINI_API_KEY not found. Behavior prediction will use mock data.")

    async def predict_buyer_behavior(
        self,
        persona_data: Dict[str, Any],
        product_info: Dict[str, Any],
        market_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Predict comprehensive buyer behavior for a persona.

        Args:
            persona_data: Complete buyer persona information
            product_info: Product/service details
            market_context: Optional market context and competitive landscape

        Returns:
            Comprehensive behavior predictions
        """
        try:
            # SIEMPRE usar IA si está disponible - NO más mock data por defecto
            if self.gemini_model:
                logger.info("Using REAL AI for behavior prediction with Gemini")
                return await self._predict_with_ai(persona_data, product_info, market_context)
            else:
                logger.warning("Gemini not available, falling back to enhanced mock data")
                return await self._predict_with_mock_data(persona_data, product_info)

        except Exception as e:
            logger.error(f"Error predicting buyer behavior: {e}")
            return {
                "status": "error",
                "error_message": f"Failed to predict behavior: {str(e)}"
            }

    async def _predict_with_ai(
        self,
        persona_data: Dict[str, Any],
        product_info: Dict[str, Any],
        market_context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Predict behavior using Gemini AI."""

        prompt = self._create_behavior_prediction_prompt(persona_data, product_info, market_context)

        try:
            logger.info("Generating AI behavior predictions with Gemini...")
            response = self.gemini_model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.4,  # Slightly higher for more creative insights
                    top_p=0.9,
                    top_k=40,
                    max_output_tokens=4096,
                )
            )
            logger.info("AI behavior prediction completed successfully")

            # Parse the AI response
            predictions = self._parse_ai_predictions(response.text)

            return {
                "status": "success",
                "predictions": predictions,
                "generated_at": datetime.now().isoformat(),
                "confidence_score": predictions.get("confidence_score", 0.75)
            }

        except Exception as e:
            logger.error(f"Error in AI behavior prediction: {e}")
            return await self._predict_with_mock_data(persona_data, product_info)

    def _create_behavior_prediction_prompt(
        self,
        persona_data: Dict[str, Any],
        product_info: Dict[str, Any],
        market_context: Optional[Dict[str, Any]]
    ) -> str:
        """Create a comprehensive prompt for behavior prediction."""

        persona_summary = f"""
        BUYER PERSONA:
        - Nombre: {persona_data.get('name', 'Unknown')}
        - Edad: {persona_data.get('age', 35)}
        - Cargo: {persona_data.get('job', {}).get('title', 'Professional')}
        - Industria: {persona_data.get('job', {}).get('industry', 'Technology')}
        - Ingresos: {persona_data.get('income_level', 'Medium')}
        - Objetivos: {', '.join(persona_data.get('goals', []))}
        - Desafíos: {', '.join(persona_data.get('challenges', []))}
        - Canales de comunicación: {', '.join(persona_data.get('communication_channels', []))}
        - Proceso de compra: {persona_data.get('buying_process', {})}
        """

        product_summary = f"""
        PRODUCTO/SERVICIO:
        - Descripción: {product_info.get('description', 'Not specified')}
        - Industria: {product_info.get('industry', 'Technology')}
        - Precio estimado: {product_info.get('price_range', 'Not specified')}
        """

        market_summary = ""
        if market_context:
            market_summary = f"""
            CONTEXTO DE MERCADO:
            - Competidores: {market_context.get('competitors', 'Not specified')}
            - Tendencias: {market_context.get('trends', 'Not specified')}
            """

        prompt = f"""
        Eres un experto en análisis de comportamiento del consumidor, psicología de ventas y predicción emocional con 20+ años de experiencia. Analiza el siguiente buyer persona y producto para predecir su comportamiento de compra con análisis emocional profundo y científico.

        {persona_summary}

        {product_summary}

        {market_summary}

        INSTRUCCIONES AVANZADAS:
        1. Realiza un análisis psicológico profundo basado en personalidad, motivaciones y emociones
        2. Identifica patrones emocionales específicos y triggers de decisión únicos
        3. Calcula timing óptimo basado en ciclos de trabajo, industria, personalidad y estacionalidad
        4. Proporciona insights accionables y específicos para equipos de ventas
        5. Considera factores culturales, generacionales y de industria
        6. Analiza el perfil de riesgo y la propensión al cambio
        7. Evalúa la influencia de stakeholders y decisores secundarios

        FORMATO DE RESPUESTA (JSON):
        {{
          "purchase_probability": {{
            "score": número_entre_0_y_100,
            "factors": ["factor1", "factor2", "factor3"],
            "timeline": "inmediato/1-3_meses/3-6_meses/6-12_meses/más_de_1_año",
            "confidence_level": "alta/media/baja"
          }},
          "emotional_analysis": {{
            "primary_emotions": ["emoción_dominante", "emoción_secundaria"],
            "stress_triggers": ["trigger1", "trigger2", "trigger3"],
            "motivation_drivers": ["motivador1", "motivador2"],
            "decision_making_style": "analítico/intuitivo/colaborativo/impulsivo",
            "communication_tone_preference": "formal/casual/técnico/consultivo",
            "trust_building_factors": ["factor1", "factor2", "factor3"],
            "emotional_barriers": ["barrera1", "barrera2"],
            "excitement_triggers": ["trigger1", "trigger2"]
          }},
          "intelligent_timing": {{
            "optimal_contact_windows": {{
              "primary": {{"day": "día", "time": "hora", "probability": número_0_100}},
              "secondary": {{"day": "día", "time": "hora", "probability": número_0_100}},
              "tertiary": {{"day": "día", "time": "hora", "probability": número_0_100}}
            }},
            "avoid_periods": [
              {{"period": "descripción", "reason": "razón_específica"}}
            ],
            "follow_up_cadence": {{
              "initial_response_time": "tiempo_esperado",
              "follow_up_intervals": ["intervalo1", "intervalo2"],
              "max_attempts": número,
              "escalation_timing": "cuándo_escalar"
            }},
            "seasonal_patterns": {{
              "high_activity_periods": ["período1", "período2"],
              "low_activity_periods": ["período1", "período2"],
              "budget_cycles": "descripción_ciclo_presupuesto"
            }},
            "industry_timing": {{
              "business_cycles": "descripción_ciclos",
              "decision_seasons": ["temporada1", "temporada2"],
              "competitive_timing": "cuándo_actúa_competencia"
            }}
          }},
          "conversion_channels": [
            {{
              "channel": "nombre_del_canal",
              "effectiveness": número_entre_0_y_100,
              "reasoning": "explicación_específica",
              "emotional_appeal": "qué_emoción_activa",
              "optimal_message_type": "tipo_de_mensaje"
            }}
          ],
          "price_sensitivity": {{
            "willing_to_pay": "rango_de_precio",
            "price_factors": ["factor1", "factor2"],
            "discount_sensitivity": número_entre_0_y_100,
            "payment_preferences": ["tarjeta", "transferencia", "etc"],
            "budget_decision_process": "descripción_proceso",
            "price_anchoring_strategy": "estrategia_recomendada"
          }},
          "likely_objections": [
            {{
              "objection": "objeción_específica",
              "probability": número_entre_0_y_100,
              "emotional_root": "emoción_subyacente",
              "counter_strategy": "estrategia_para_superar",
              "timing_to_address": "cuándo_abordar"
            }}
          ],
          "decision_influencers": [
            {{
              "influencer": "tipo_de_influenciador",
              "impact_level": "alto/medio/bajo",
              "approach": "cómo_influenciar",
              "emotional_connection": "tipo_de_conexión",
              "influence_timing": "cuándo_involucrar"
            }}
          ],
          "content_preferences": {{
            "formats": ["video", "pdf", "webinar", "etc"],
            "topics": ["tema1", "tema2", "tema3"],
            "tone": "profesional/casual/técnico/etc",
            "emotional_hooks": ["hook1", "hook2"],
            "content_timing": "cuándo_consumir_contenido"
          }},
          "lead_quality_score": {{
            "overall_score": número_entre_0_y_100,
            "scoring_factors": {{
              "budget_fit": número_0_100,
              "authority_level": número_0_100,
              "need_urgency": número_0_100,
              "solution_fit": número_0_100,
              "timing_alignment": número_0_100
            }},
            "qualification_status": "hot/warm/cold/nurture",
            "next_best_action": "acción_recomendada"
          }},
          "confidence_score": número_entre_0_y_1
        }}

        IMPORTANTE: Responde SOLO con el JSON válido, sin texto adicional. Sé específico y actionable en todas las recomendaciones.
        """

        return prompt.strip()

    def _parse_ai_predictions(self, response: str) -> Dict[str, Any]:
        """Parse AI response and extract predictions."""
        try:
            # Clean the response
            response = response.strip()
            logger.info(f"Parsing AI response (length: {len(response)})")

            # Find JSON content
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1

            if start_idx == -1 or end_idx == 0:
                logger.warning("No JSON found in AI response, using default predictions")
                return self._get_default_predictions()

            json_str = response[start_idx:end_idx]
            logger.info("JSON extracted successfully from AI response")

            predictions = json.loads(json_str)
            logger.info("AI predictions parsed successfully")

            # Validate and set defaults
            validated_predictions = self._validate_predictions(predictions)
            logger.info("AI predictions validated and enhanced")

            return validated_predictions

        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error in AI predictions: {e}")
            return self._get_default_predictions()
        except Exception as e:
            logger.error(f"Error parsing AI predictions: {e}")
            return self._get_default_predictions()

    def _validate_predictions(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and normalize prediction data with enhanced emotional and timing analysis."""

        # Ensure all required fields exist with defaults
        validated = {
            "purchase_probability": predictions.get("purchase_probability", {
                "score": 65,
                "factors": ["Necesidad identificada", "Presupuesto disponible"],
                "timeline": "3-6_meses",
                "confidence_level": "media"
            }),
            "emotional_analysis": predictions.get("emotional_analysis", {
                "primary_emotions": ["optimismo", "cautela"],
                "stress_triggers": ["presión de tiempo", "presupuesto limitado"],
                "motivation_drivers": ["eficiencia", "reconocimiento"],
                "decision_making_style": "analítico",
                "communication_tone_preference": "profesional",
                "trust_building_factors": ["referencias", "casos de éxito", "transparencia"],
                "emotional_barriers": ["miedo al cambio", "incertidumbre sobre ROI"],
                "excitement_triggers": ["innovación", "resultados rápidos"]
            }),
            "intelligent_timing": predictions.get("intelligent_timing", {
                "optimal_contact_windows": {
                    "primary": {"day": "martes", "time": "10:00-11:00", "probability": 85},
                    "secondary": {"day": "jueves", "time": "15:00-16:00", "probability": 75},
                    "tertiary": {"day": "miércoles", "time": "9:00-10:00", "probability": 70}
                },
                "avoid_periods": [
                    {"period": "lunes temprano", "reason": "planificación semanal"},
                    {"period": "viernes tarde", "reason": "cierre de semana"}
                ],
                "follow_up_cadence": {
                    "initial_response_time": "24-48 horas",
                    "follow_up_intervals": ["3 días", "1 semana", "2 semanas"],
                    "max_attempts": 5,
                    "escalation_timing": "después de 3 intentos"
                },
                "seasonal_patterns": {
                    "high_activity_periods": ["enero-marzo", "septiembre-noviembre"],
                    "low_activity_periods": ["julio-agosto", "diciembre"],
                    "budget_cycles": "presupuestos anuales en Q4, aprobaciones en Q1"
                },
                "industry_timing": {
                    "business_cycles": "planificación trimestral",
                    "decision_seasons": ["inicio de año", "post-vacaciones"],
                    "competitive_timing": "evaluaciones en Q2 y Q4"
                }
            }),
            "conversion_channels": predictions.get("conversion_channels", [
                {
                    "channel": "Email profesional",
                    "effectiveness": 85,
                    "reasoning": "Canal preferido para comunicación B2B formal",
                    "emotional_appeal": "confianza y profesionalismo",
                    "optimal_message_type": "consultivo y educativo"
                }
            ]),
            "price_sensitivity": predictions.get("price_sensitivity", {
                "willing_to_pay": "$2,000-$10,000",
                "price_factors": ["ROI demostrable", "Calidad del soporte"],
                "discount_sensitivity": 65,
                "payment_preferences": ["transferencia bancaria", "tarjeta corporativa"],
                "budget_decision_process": "requiere aprobación de superiores",
                "price_anchoring_strategy": "mostrar valor antes que precio"
            }),
            "likely_objections": predictions.get("likely_objections", [
                {
                    "objection": "Precio demasiado elevado",
                    "probability": 75,
                    "emotional_root": "miedo a tomar mala decisión",
                    "counter_strategy": "mostrar ROI específico y casos similares",
                    "timing_to_address": "después de demostrar valor"
                }
            ]),
            "decision_influencers": predictions.get("decision_influencers", [
                {
                    "influencer": "Colegas del sector",
                    "impact_level": "alto",
                    "approach": "testimonios y referencias",
                    "emotional_connection": "confianza peer-to-peer",
                    "influence_timing": "durante evaluación"
                }
            ]),
            "content_preferences": predictions.get("content_preferences", {
                "formats": ["PDF ejecutivo", "video demo", "webinar"],
                "topics": ["ROI y métricas", "casos de éxito", "implementación"],
                "tone": "profesional y consultivo",
                "emotional_hooks": ["éxito empresarial", "eficiencia operativa"],
                "content_timing": "mañanas para contenido técnico, tardes para casos de éxito"
            }),
            "lead_quality_score": predictions.get("lead_quality_score", {
                "overall_score": 75,
                "scoring_factors": {
                    "budget_fit": 80,
                    "authority_level": 70,
                    "need_urgency": 65,
                    "solution_fit": 85,
                    "timing_alignment": 75
                },
                "qualification_status": "warm",
                "next_best_action": "enviar caso de estudio relevante"
            }),
            "confidence_score": min(max(predictions.get("confidence_score", 0.75), 0), 1)
        }

        return validated

    async def _predict_with_mock_data(
        self,
        persona_data: Dict[str, Any],
        product_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate enhanced mock behavior predictions when AI is not available."""

        logger.info("Generating enhanced mock behavior predictions")

        # Base predictions on persona characteristics
        age = persona_data.get('age', 35)
        job_title = persona_data.get('job', {}).get('title', '').lower()
        industry = persona_data.get('job', {}).get('industry', '').lower()
        income_level = persona_data.get('income_level', 'medium').lower()
        goals = persona_data.get('goals', [])
        challenges = persona_data.get('challenges', [])

        # Enhanced scoring algorithm
        purchase_score = 50  # Base score

        # Job title influence
        if any(title in job_title for title in ['director', 'manager', 'ceo', 'founder']):
            purchase_score += 20
        elif any(title in job_title for title in ['senior', 'lead', 'head']):
            purchase_score += 15
        elif any(title in job_title for title in ['coordinator', 'specialist']):
            purchase_score += 10

        # Industry influence
        if any(ind in industry for ind in ['tech', 'software', 'startup']):
            purchase_score += 15
        elif any(ind in industry for ind in ['finance', 'consulting']):
            purchase_score += 10

        # Age influence
        if 30 <= age <= 45:  # Prime decision-making age
            purchase_score += 10
        elif age > 45:
            purchase_score += 5

        # Income influence
        if 'alto' in income_level or 'high' in income_level:
            purchase_score += 15
        elif 'medio' in income_level or 'medium' in income_level:
            purchase_score += 5

        # Goals and challenges influence
        if any(goal for goal in goals if any(keyword in goal.lower() for keyword in ['efficiency', 'growth', 'automation'])):
            purchase_score += 10

        purchase_score = min(max(purchase_score, 20), 95)  # Keep between 20-95

        # Generate contextual predictions
        predictions = self._get_enhanced_default_predictions(persona_data, product_info, purchase_score)

        return {
            "status": "success",
            "predictions": predictions,
            "generated_at": datetime.now().isoformat(),
            "confidence_score": 0.75,  # Lower confidence for mock data
            "data_source": "enhanced_algorithm"  # Indicate this is not AI
        }

    def _get_enhanced_default_predictions(
        self,
        persona_data: Dict[str, Any],
        product_info: Dict[str, Any],
        purchase_score: int
    ) -> Dict[str, Any]:
        """Get enhanced default predictions based on persona data."""

        age = persona_data.get('age', 35)
        job_title = persona_data.get('job', {}).get('title', 'Professional')
        industry = persona_data.get('job', {}).get('industry', 'Technology')
        goals = persona_data.get('goals', [])
        challenges = persona_data.get('challenges', [])

        # Contextual timeline based on industry and role
        timeline = "3-6_meses"
        if any(title in job_title.lower() for title in ['director', 'ceo', 'founder']):
            timeline = "1-3_meses"  # Executives decide faster
        elif 'startup' in industry.lower():
            timeline = "1-3_meses"  # Startups move fast
        elif any(ind in industry.lower() for ind in ['government', 'healthcare']):
            timeline = "6-12_meses"  # Regulated industries slower

        # Contextual objections based on challenges
        objections = []
        if any('budget' in challenge.lower() or 'cost' in challenge.lower() for challenge in challenges):
            objections.append({
                "objection": "Preocupación por presupuesto limitado",
                "probability": 85,
                "emotional_root": "miedo a exceder presupuesto",
                "counter_strategy": "mostrar ROI específico y opciones de pago flexibles",
                "timing_to_address": "después de demostrar valor inicial"
            })

        if any('time' in challenge.lower() or 'tiempo' in challenge.lower() for challenge in challenges):
            objections.append({
                "objection": "Falta de tiempo para implementación",
                "probability": 70,
                "emotional_root": "estrés por carga de trabajo",
                "counter_strategy": "enfatizar facilidad de implementación y soporte",
                "timing_to_address": "durante presentación de solución"
            })

        # Default objections if none detected
        if not objections:
            objections = [
                {
                    "objection": "Necesidad de evaluar alternativas",
                    "probability": 60,
                    "emotional_root": "cautela profesional",
                    "counter_strategy": "proporcionar comparación competitiva",
                    "timing_to_address": "durante fase de evaluación"
                }
            ]

        return {
            "purchase_probability": {
                "score": purchase_score,
                "factors": [
                    f"Rol de {job_title} con autoridad de decisión",
                    f"Objetivos alineados: {goals[0] if goals else 'crecimiento'}",
                    f"Industria {industry} con necesidades específicas"
                ],
                "timeline": timeline,
                "confidence_level": "media"
            },
            "emotional_analysis": {
                "primary_emotions": ["optimismo cauteloso", "profesionalismo"],
                "stress_triggers": challenges[:2] if challenges else ["presión de tiempo", "presupuesto"],
                "motivation_drivers": goals[:2] if goals else ["eficiencia", "crecimiento"],
                "decision_making_style": "analítico" if age > 35 else "colaborativo",
                "communication_tone_preference": "profesional",
                "trust_building_factors": ["referencias del sector", "casos de éxito", "transparencia"],
                "emotional_barriers": ["miedo al cambio", "incertidumbre sobre implementación"],
                "excitement_triggers": ["innovación", "resultados medibles"]
            },
            "intelligent_timing": {
                "optimal_contact_windows": {
                    "primary": {"day": "martes", "time": "10:00-11:00", "probability": 80},
                    "secondary": {"day": "jueves", "time": "15:00-16:00", "probability": 70},
                    "tertiary": {"day": "miércoles", "time": "9:00-10:00", "probability": 65}
                },
                "avoid_periods": [
                    {"period": "lunes temprano", "reason": "planificación semanal"},
                    {"period": "viernes tarde", "reason": "cierre de semana"}
                ],
                "follow_up_cadence": {
                    "initial_response_time": "24-48 horas",
                    "follow_up_intervals": ["3 días", "1 semana", "2 semanas"],
                    "max_attempts": 5,
                    "escalation_timing": "después de 3 intentos sin respuesta"
                }
            },
            "conversion_channels": [
                {
                    "channel": "Email profesional",
                    "effectiveness": 85,
                    "reasoning": f"Canal preferido para {industry}",
                    "emotional_appeal": "confianza y profesionalismo",
                    "optimal_message_type": "consultivo y educativo"
                },
                {
                    "channel": "LinkedIn",
                    "effectiveness": 75,
                    "reasoning": "Red profesional activa",
                    "emotional_appeal": "networking y credibilidad",
                    "optimal_message_type": "insights de industria"
                }
            ],
            "price_sensitivity": {
                "willing_to_pay": "$2,000-$15,000" if 'director' in job_title.lower() else "$500-$5,000",
                "price_factors": ["ROI demostrable", "Calidad del soporte"],
                "discount_sensitivity": 60 if purchase_score > 70 else 75,
                "payment_preferences": ["transferencia bancaria", "tarjeta corporativa"],
                "budget_decision_process": "requiere aprobación" if 'coordinator' in job_title.lower() else "autoridad propia",
                "price_anchoring_strategy": "mostrar valor antes que precio"
            },
            "likely_objections": objections,
            "decision_influencers": [
                {
                    "influencer": "Equipo técnico",
                    "impact_level": "alto" if 'tech' in industry.lower() else "medio",
                    "approach": "demos técnicas y documentación",
                    "emotional_connection": "confianza en capacidades técnicas",
                    "influence_timing": "durante evaluación técnica"
                }
            ],
            "content_preferences": {
                "formats": ["PDF ejecutivo", "video demo", "webinar"],
                "topics": ["ROI y métricas", "casos de éxito", f"soluciones para {industry}"],
                "tone": "profesional y consultivo",
                "emotional_hooks": ["éxito empresarial", "eficiencia operativa"],
                "content_timing": "mañanas para contenido técnico"
            },
            "lead_quality_score": {
                "overall_score": purchase_score,
                "scoring_factors": {
                    "budget_fit": min(purchase_score + 10, 90),
                    "authority_level": 85 if 'director' in job_title.lower() else 60,
                    "need_urgency": 70 if timeline == "1-3_meses" else 50,
                    "solution_fit": purchase_score,
                    "timing_alignment": 75
                },
                "qualification_status": "hot" if purchase_score > 80 else "warm" if purchase_score > 60 else "cold",
                "next_best_action": f"enviar caso de estudio de {industry}"
            },
            "confidence_score": 0.75
        }

    def _get_default_predictions(self) -> Dict[str, Any]:
        """Get default prediction structure."""
        return {
            "purchase_probability": {
                "score": 65,
                "factors": [
                    "Necesidad identificada del producto/servicio",
                    "Presupuesto disponible en la organización",
                    "Autoridad para tomar decisiones de compra"
                ],
                "timeline": "3-6_meses"
            },
            "conversion_channels": [
                {
                    "channel": "Email profesional",
                    "effectiveness": 85,
                    "reasoning": "Canal preferido para comunicación B2B formal"
                },
                {
                    "channel": "LinkedIn",
                    "effectiveness": 75,
                    "reasoning": "Red profesional activa, ideal para networking"
                },
                {
                    "channel": "Llamada telefónica",
                    "effectiveness": 60,
                    "reasoning": "Efectivo para seguimiento después de contacto inicial"
                },
                {
                    "channel": "Webinar/Demo",
                    "effectiveness": 80,
                    "reasoning": "Permite mostrar valor del producto en tiempo real"
                }
            ],
            "optimal_contact_timing": {
                "best_days": ["martes", "miércoles", "jueves"],
                "best_hours": "9:00-11:00, 14:00-16:00",
                "timezone": "UTC-5",
                "frequency": "quincenal"
            },
            "price_sensitivity": {
                "willing_to_pay": "$2,000-$10,000",
                "price_factors": ["ROI demostrable", "Calidad del soporte", "Facilidad de implementación"],
                "discount_sensitivity": 65,
                "payment_preferences": ["transferencia bancaria", "tarjeta corporativa", "facturación mensual"]
            },
            "likely_objections": [
                {
                    "objection": "Precio demasiado elevado para el presupuesto actual",
                    "probability": 75,
                    "counter_strategy": "Mostrar ROI específico y opciones de pago flexibles"
                },
                {
                    "objection": "Necesidad de aprobación de superiores",
                    "probability": 60,
                    "counter_strategy": "Proporcionar materiales para presentar a stakeholders"
                },
                {
                    "objection": "Preocupación por tiempo de implementación",
                    "probability": 50,
                    "counter_strategy": "Mostrar timeline claro y soporte durante implementación"
                }
            ],
            "decision_influencers": [
                {
                    "influencer": "Colegas del sector",
                    "impact_level": "alto",
                    "approach": "Testimonios y casos de éxito de empresas similares"
                },
                {
                    "influencer": "Superiores directos",
                    "impact_level": "alto",
                    "approach": "Materiales ejecutivos enfocados en ROI"
                },
                {
                    "influencer": "Equipo técnico",
                    "impact_level": "medio",
                    "approach": "Documentación técnica y demos detalladas"
                }
            ],
            "content_preferences": {
                "formats": ["PDF ejecutivo", "Video demo", "Webinar interactivo", "Caso de estudio"],
                "topics": ["ROI y métricas", "Casos de éxito", "Proceso de implementación", "Soporte y training"],
                "tone": "profesional y consultivo"
            },
            "confidence_score": 0.75
        }


# Create singleton instance
behavior_prediction_service = BehaviorPredictionService()

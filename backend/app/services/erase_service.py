"""Service for erasing objects from images using Stability AI v2beta API."""

import logging
import httpx
import io
from typing import Optional
from fastapi import HTTPEx<PERSON>, UploadFile
from PIL import Image

from app.schemas.erase import EraseRequest, EraseResponse
from app.core.config import settings

logger = logging.getLogger(__name__)


def get_stability_headers_json():
    """Get headers for Stability AI API requests expecting JSON response."""
    return {
        "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
        "Accept": "application/json"
    }


async def validate_erase_image(image_file: UploadFile) -> bytes:
    """
    Validate and process the input image for erase operation.
    
    Args:
        image_file: The uploaded image file
        
    Returns:
        bytes: The processed image content
        
    Raises:
        HTTPException: If validation fails
    """
    try:
        # Leer el contenido del archivo
        image_content = await image_file.read()
        
        # Validar tamaño del archivo (máximo 10MB según documentación)
        max_size = 10 * 1024 * 1024  # 10MB
        if len(image_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail=f"Image file too large. Maximum size is {max_size / (1024*1024):.1f}MB"
            )
        
        # Validar que sea una imagen válida
        try:
            with Image.open(io.BytesIO(image_content)) as img:
                # Validar dimensiones mínimas (64x64 según documentación)
                if img.width < 64 or img.height < 64:
                    raise HTTPException(
                        status_code=400,
                        detail="Image dimensions too small. Minimum size is 64x64 pixels"
                    )
                
                # Validar dimensiones máximas (9,437,184 pixels total según documentación)
                total_pixels = img.width * img.height
                max_pixels = 9_437_184
                if total_pixels > max_pixels:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Image too large. Maximum total pixels: {max_pixels:,}"
                    )
                
                # Validar formato
                if img.format not in ['JPEG', 'PNG', 'WEBP']:
                    raise HTTPException(
                        status_code=400,
                        detail="Unsupported image format. Supported formats: JPEG, PNG, WebP"
                    )
                
                logger.info(f"Image validated: {img.width}x{img.height}, format: {img.format}")
                
        except Exception as e:
            if isinstance(e, HTTPException):
                raise
            raise HTTPException(status_code=400, detail="Invalid image file")
        
        return image_content
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating image: {e}")
        raise HTTPException(status_code=400, detail="Error processing image file")


async def validate_mask_image(mask_file: UploadFile) -> bytes:
    """
    Validate and process the mask image for erase operation.
    
    Args:
        mask_file: The uploaded mask file
        
    Returns:
        bytes: The processed mask content
        
    Raises:
        HTTPException: If validation fails
    """
    try:
        # Leer el contenido del archivo
        mask_content = await mask_file.read()
        
        # Validar tamaño del archivo
        max_size = 10 * 1024 * 1024  # 10MB
        if len(mask_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail=f"Mask file too large. Maximum size is {max_size / (1024*1024):.1f}MB"
            )
        
        # Validar que sea una imagen válida
        try:
            with Image.open(io.BytesIO(mask_content)) as img:
                # Validar formato
                if img.format not in ['JPEG', 'PNG', 'WEBP']:
                    raise HTTPException(
                        status_code=400,
                        detail="Unsupported mask format. Supported formats: JPEG, PNG, WebP"
                    )
                
                logger.info(f"Mask validated: {img.width}x{img.height}, format: {img.format}")
                
        except Exception as e:
            if isinstance(e, HTTPException):
                raise
            raise HTTPException(status_code=400, detail="Invalid mask file")
        
        return mask_content
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating mask: {e}")
        raise HTTPException(status_code=400, detail="Error processing mask file")


async def erase_objects_stability(
    image_file: UploadFile,
    mask_file: Optional[UploadFile] = None,
    request: EraseRequest = EraseRequest()
) -> EraseResponse:
    """
    Erase objects from an image using Stability AI v2beta API.
    
    Args:
        image_file: The image file to process
        mask_file: Optional mask file indicating areas to erase
        request: The erase parameters
        
    Returns:
        EraseResponse: The processed image and metadata
        
    Raises:
        HTTPException: If erase operation fails
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # Validar la imagen principal
        image_content = await validate_erase_image(image_file)
        
        # URL de la API v2beta para erase
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/edit/erase"
        
        # Headers para recibir respuesta JSON con base64
        headers = get_stability_headers_json()

        # Preparar FormData
        form_data = {
            "grow_mask": request.grow_mask,
            "output_format": request.output_format
        }

        # Agregar seed si se especifica
        if request.seed and request.seed > 0:
            form_data["seed"] = request.seed

        # Preparar archivos para upload
        files = {
            "image": ("image.jpg", io.BytesIO(image_content), image_file.content_type)
        }

        # Agregar máscara si se proporciona
        if mask_file:
            mask_content = await validate_mask_image(mask_file)
            files["mask"] = ("mask.jpg", io.BytesIO(mask_content), mask_file.content_type)

        logger.info(f"Erasing objects using Stability AI v2beta")
        logger.info(f"URL: {url}")
        logger.info(f"Grow mask: {request.grow_mask}")
        logger.info(f"Output format: {request.output_format}")
        logger.info(f"Has mask: {mask_file is not None}")

        async with httpx.AsyncClient(timeout=120.0) as client:  # 2 minutos timeout
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )
            
            logger.info(f"Stability AI response status: {response.status_code}")
            
            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI error: {error_text}"
                )

            # Procesar respuesta JSON
            result = response.json()
            image_data = result.get("image")
            
            if not image_data:
                raise ValueError("No image data in response")

            return EraseResponse(
                image=image_data,
                seed=result.get("seed"),
                finish_reason=result.get("finish_reason", "SUCCESS")
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in erase operation: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to erase objects: {str(e)}"
        )

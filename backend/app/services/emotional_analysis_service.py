"""Service for advanced emotional analysis of buyer personas."""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class EmotionalProfile:
    """Data class for emotional profile analysis."""
    primary_emotions: List[str]
    stress_triggers: List[str]
    motivation_drivers: List[str]
    decision_making_style: str
    communication_tone_preference: str
    trust_building_factors: List[str]
    emotional_barriers: List[str]
    excitement_triggers: List[str]
    confidence_score: float


class EmotionalAnalysisService:
    """Service for analyzing emotional patterns and psychological triggers."""

    def __init__(self):
        """Initialize the emotional analysis service."""
        self.personality_patterns = self._load_personality_patterns()
        self.industry_emotional_patterns = self._load_industry_patterns()
        self.age_emotional_patterns = self._load_age_patterns()

    def analyze_emotional_profile(
        self,
        persona_data: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> EmotionalProfile:
        """
        Analyze emotional profile based on persona characteristics.
        
        Args:
            persona_data: Complete buyer persona information
            context: Additional context for analysis
            
        Returns:
            EmotionalProfile with detailed emotional analysis
        """
        try:
            # Extract key characteristics
            age = persona_data.get('age', 35)
            job_title = persona_data.get('job', {}).get('title', '')
            industry = persona_data.get('job', {}).get('industry', 'Technology')
            goals = persona_data.get('goals', [])
            challenges = persona_data.get('challenges', [])
            
            # Analyze different emotional aspects
            primary_emotions = self._analyze_primary_emotions(age, job_title, goals, challenges)
            stress_triggers = self._identify_stress_triggers(challenges, job_title, industry)
            motivation_drivers = self._identify_motivation_drivers(goals, age, job_title)
            decision_style = self._determine_decision_making_style(job_title, age, industry)
            communication_tone = self._determine_communication_preference(job_title, age, industry)
            trust_factors = self._identify_trust_building_factors(job_title, industry, age)
            emotional_barriers = self._identify_emotional_barriers(challenges, age, job_title)
            excitement_triggers = self._identify_excitement_triggers(goals, age, industry)
            
            # Calculate confidence score
            confidence = self._calculate_emotional_confidence(persona_data)
            
            return EmotionalProfile(
                primary_emotions=primary_emotions,
                stress_triggers=stress_triggers,
                motivation_drivers=motivation_drivers,
                decision_making_style=decision_style,
                communication_tone_preference=communication_tone,
                trust_building_factors=trust_factors,
                emotional_barriers=emotional_barriers,
                excitement_triggers=excitement_triggers,
                confidence_score=confidence
            )
            
        except Exception as e:
            logger.error(f"Error analyzing emotional profile: {e}")
            return self._get_default_emotional_profile()

    def _analyze_primary_emotions(
        self,
        age: int,
        job_title: str,
        goals: List[str],
        challenges: List[str]
    ) -> List[str]:
        """Analyze primary emotional states."""
        emotions = []
        
        # Age-based emotional tendencies
        if age < 30:
            emotions.extend(["ambición", "optimismo", "impaciencia"])
        elif age < 45:
            emotions.extend(["determinación", "pragmatismo", "cautela"])
        else:
            emotions.extend(["experiencia", "estabilidad", "selectividad"])
        
        # Job-level emotional patterns
        if any(word in job_title.lower() for word in ["director", "ceo", "vp"]):
            emotions.extend(["confianza", "responsabilidad", "presión"])
        elif any(word in job_title.lower() for word in ["manager", "lead"]):
            emotions.extend(["equilibrio", "coordinación", "eficiencia"])
        else:
            emotions.extend(["crecimiento", "aprendizaje", "colaboración"])
        
        # Goal-driven emotions
        goal_emotions = {
            "aumentar ventas": ["ambición", "competitividad"],
            "reducir costos": ["prudencia", "eficiencia"],
            "mejorar eficiencia": ["optimización", "perfeccionismo"],
            "crecer": ["expansión", "visión"]
        }
        
        for goal in goals:
            for key, emots in goal_emotions.items():
                if key.lower() in goal.lower():
                    emotions.extend(emots)
        
        # Remove duplicates and return top emotions
        unique_emotions = list(dict.fromkeys(emotions))
        return unique_emotions[:3]

    def _identify_stress_triggers(
        self,
        challenges: List[str],
        job_title: str,
        industry: str
    ) -> List[str]:
        """Identify key stress triggers."""
        triggers = []
        
        # Challenge-based triggers
        challenge_triggers = {
            "tiempo": ["presión de tiempo", "deadlines ajustados", "multitasking"],
            "presupuesto": ["limitaciones financieras", "justificar ROI", "aprobaciones"],
            "personal": ["falta de recursos", "capacitación del equipo", "rotación"],
            "tecnología": ["adopción de nuevas herramientas", "integración compleja", "curva de aprendizaje"]
        }
        
        for challenge in challenges:
            for key, trigger_list in challenge_triggers.items():
                if key.lower() in challenge.lower():
                    triggers.extend(trigger_list)
        
        # Industry-specific triggers
        industry_triggers = self.industry_emotional_patterns.get(industry.lower(), {}).get('stress_triggers', [])
        triggers.extend(industry_triggers)
        
        # Role-specific triggers
        if any(word in job_title.lower() for word in ["director", "ceo"]):
            triggers.extend(["responsabilidad final", "decisiones estratégicas", "presión de resultados"])
        elif any(word in job_title.lower() for word in ["manager"]):
            triggers.extend(["gestión de equipo", "reportes a superiores", "coordinación"])
        
        return list(dict.fromkeys(triggers))[:4]

    def _identify_motivation_drivers(
        self,
        goals: List[str],
        age: int,
        job_title: str
    ) -> List[str]:
        """Identify key motivation drivers."""
        drivers = []
        
        # Age-based motivations
        if age < 35:
            drivers.extend(["reconocimiento", "crecimiento profesional", "aprendizaje"])
        elif age < 50:
            drivers.extend(["estabilidad", "eficiencia", "liderazgo"])
        else:
            drivers.extend(["legado", "mentoría", "optimización"])
        
        # Goal-based motivations
        goal_motivations = {
            "ventas": ["resultados medibles", "comisiones", "competencia"],
            "eficiencia": ["optimización", "automatización", "simplicidad"],
            "crecimiento": ["expansión", "oportunidades", "innovación"],
            "ahorro": ["ROI", "reducción de costos", "eficiencia"]
        }
        
        for goal in goals:
            for key, motivs in goal_motivations.items():
                if key.lower() in goal.lower():
                    drivers.extend(motivs)
        
        # Role-based motivations
        if any(word in job_title.lower() for word in ["director", "ceo"]):
            drivers.extend(["impacto estratégico", "resultados empresariales", "liderazgo"])
        
        return list(dict.fromkeys(drivers))[:3]

    def _determine_decision_making_style(
        self,
        job_title: str,
        age: int,
        industry: str
    ) -> str:
        """Determine decision-making style."""
        
        # Executive level - more analytical
        if any(word in job_title.lower() for word in ["director", "ceo", "vp"]):
            return "analítico-estratégico"
        
        # Manager level - collaborative
        elif any(word in job_title.lower() for word in ["manager", "lead", "head"]):
            return "colaborativo-consultivo"
        
        # Age-based tendencies
        elif age < 30:
            return "intuitivo-rápido"
        elif age > 50:
            return "experiencial-cauteloso"
        
        # Industry patterns
        elif industry.lower() in ["technology", "startup"]:
            return "ágil-experimental"
        elif industry.lower() in ["finance", "healthcare"]:
            return "analítico-riguroso"
        
        return "equilibrado-pragmático"

    def _determine_communication_preference(
        self,
        job_title: str,
        age: int,
        industry: str
    ) -> str:
        """Determine communication tone preference."""
        
        # Executive level
        if any(word in job_title.lower() for word in ["director", "ceo", "vp"]):
            return "ejecutivo-directo"
        
        # Technical roles
        elif any(word in job_title.lower() for word in ["engineer", "developer", "technical"]):
            return "técnico-detallado"
        
        # Sales/Marketing roles
        elif any(word in job_title.lower() for word in ["sales", "marketing", "business"]):
            return "persuasivo-relacional"
        
        # Age considerations
        elif age > 50:
            return "formal-respetuoso"
        elif age < 30:
            return "casual-dinámico"
        
        return "profesional-consultivo"

    def _identify_trust_building_factors(
        self,
        job_title: str,
        industry: str,
        age: int
    ) -> List[str]:
        """Identify factors that build trust."""
        factors = []
        
        # Universal trust factors
        factors.extend(["transparencia", "casos de éxito", "referencias"])
        
        # Role-specific trust factors
        if any(word in job_title.lower() for word in ["director", "ceo"]):
            factors.extend(["ROI demostrable", "soporte ejecutivo", "implementación probada"])
        elif any(word in job_title.lower() for word in ["technical", "engineer"]):
            factors.extend(["documentación técnica", "pruebas de concepto", "integración"])
        
        # Industry-specific factors
        industry_trust = self.industry_emotional_patterns.get(industry.lower(), {}).get('trust_factors', [])
        factors.extend(industry_trust)
        
        # Age-based factors
        if age > 45:
            factors.extend(["experiencia del proveedor", "estabilidad empresarial"])
        else:
            factors.extend(["innovación", "agilidad", "soporte responsive"])
        
        return list(dict.fromkeys(factors))[:4]

    def _identify_emotional_barriers(
        self,
        challenges: List[str],
        age: int,
        job_title: str
    ) -> List[str]:
        """Identify emotional barriers to purchase."""
        barriers = []
        
        # Common barriers
        barriers.extend(["miedo al cambio", "incertidumbre sobre ROI"])
        
        # Challenge-based barriers
        for challenge in challenges:
            if "tiempo" in challenge.lower():
                barriers.append("preocupación por tiempo de implementación")
            elif "presupuesto" in challenge.lower():
                barriers.append("ansiedad por justificar inversión")
            elif "personal" in challenge.lower():
                barriers.append("temor a resistencia del equipo")
        
        # Role-based barriers
        if any(word in job_title.lower() for word in ["director", "ceo"]):
            barriers.extend(["responsabilidad por decisión", "impacto en resultados"])
        
        # Age-based barriers
        if age > 50:
            barriers.append("preferencia por soluciones conocidas")
        elif age < 30:
            barriers.append("falta de autoridad para decidir")
        
        return list(dict.fromkeys(barriers))[:3]

    def _identify_excitement_triggers(
        self,
        goals: List[str],
        age: int,
        industry: str
    ) -> List[str]:
        """Identify what excites the persona."""
        triggers = []
        
        # Goal-based excitement
        for goal in goals:
            if "ventas" in goal.lower():
                triggers.extend(["resultados rápidos", "crecimiento medible"])
            elif "eficiencia" in goal.lower():
                triggers.extend(["automatización", "simplicidad"])
            elif "innovación" in goal.lower():
                triggers.extend(["tecnología avanzada", "diferenciación"])
        
        # Age-based excitement
        if age < 35:
            triggers.extend(["innovación", "crecimiento profesional"])
        else:
            triggers.extend(["estabilidad", "optimización"])
        
        # Industry patterns
        if industry.lower() in ["technology", "startup"]:
            triggers.extend(["disrupción", "escalabilidad"])
        elif industry.lower() in ["finance"]:
            triggers.extend(["precisión", "compliance"])
        
        return list(dict.fromkeys(triggers))[:3]

    def _calculate_emotional_confidence(self, persona_data: Dict[str, Any]) -> float:
        """Calculate confidence in emotional analysis."""
        confidence = 0.7  # Base confidence
        
        # More data = higher confidence
        if persona_data.get('goals'):
            confidence += 0.1
        if persona_data.get('challenges'):
            confidence += 0.1
        if persona_data.get('job', {}).get('title'):
            confidence += 0.05
        if persona_data.get('age'):
            confidence += 0.05
        
        return min(confidence, 0.95)

    def _load_personality_patterns(self) -> Dict[str, Any]:
        """Load personality-based emotional patterns."""
        return {
            "analytical": {
                "emotions": ["cautela", "precisión", "lógica"],
                "stress_triggers": ["información incompleta", "decisiones apresuradas"],
                "motivations": ["datos", "evidencia", "análisis"]
            },
            "driver": {
                "emotions": ["determinación", "impaciencia", "competitividad"],
                "stress_triggers": ["lentitud", "burocracia", "indecisión"],
                "motivations": ["resultados", "eficiencia", "control"]
            },
            "expressive": {
                "emotions": ["entusiasmo", "optimismo", "sociabilidad"],
                "stress_triggers": ["aislamiento", "detalles excesivos", "rigidez"],
                "motivations": ["reconocimiento", "interacción", "creatividad"]
            },
            "amiable": {
                "emotions": ["estabilidad", "cooperación", "paciencia"],
                "stress_triggers": ["presión", "cambios bruscos", "conflicto"],
                "motivations": ["armonía", "seguridad", "relaciones"]
            }
        }

    def _load_industry_patterns(self) -> Dict[str, Any]:
        """Load industry-specific emotional patterns."""
        return {
            "technology": {
                "stress_triggers": ["obsolescencia", "competencia", "escalabilidad"],
                "trust_factors": ["innovación", "soporte técnico", "roadmap"]
            },
            "finance": {
                "stress_triggers": ["compliance", "riesgo", "regulación"],
                "trust_factors": ["seguridad", "auditoría", "certificaciones"]
            },
            "healthcare": {
                "stress_triggers": ["regulación", "pacientes", "costos"],
                "trust_factors": ["certificaciones", "casos clínicos", "soporte"]
            },
            "retail": {
                "stress_triggers": ["estacionalidad", "inventario", "competencia"],
                "trust_factors": ["casos de éxito", "ROI", "implementación rápida"]
            }
        }

    def _load_age_patterns(self) -> Dict[str, Any]:
        """Load age-based emotional patterns."""
        return {
            "young": {  # < 30
                "emotions": ["ambición", "impaciencia", "optimismo"],
                "motivations": ["crecimiento", "aprendizaje", "reconocimiento"]
            },
            "middle": {  # 30-50
                "emotions": ["equilibrio", "pragmatismo", "responsabilidad"],
                "motivations": ["estabilidad", "eficiencia", "liderazgo"]
            },
            "senior": {  # > 50
                "emotions": ["experiencia", "cautela", "selectividad"],
                "motivations": ["legado", "optimización", "mentoría"]
            }
        }

    def _get_default_emotional_profile(self) -> EmotionalProfile:
        """Get default emotional profile for fallback."""
        return EmotionalProfile(
            primary_emotions=["pragmatismo", "cautela", "determinación"],
            stress_triggers=["presión de tiempo", "presupuesto limitado", "cambios bruscos"],
            motivation_drivers=["eficiencia", "resultados", "reconocimiento"],
            decision_making_style="analítico-colaborativo",
            communication_tone_preference="profesional-consultivo",
            trust_building_factors=["referencias", "casos de éxito", "transparencia"],
            emotional_barriers=["miedo al cambio", "incertidumbre sobre ROI"],
            excitement_triggers=["innovación", "resultados rápidos", "simplicidad"],
            confidence_score=0.75
        )


# Create singleton instance
emotional_analysis_service = EmotionalAnalysisService()

"""Service for generating AI avatars for buyer personas."""

import logging
import base64
import io
import uuid
from typing import Optional, Dict, Any, List
from PIL import Image, ImageDraw, ImageFont
import requests
import google.generativeai as genai
from app.core.config import settings
from app.services.image_service import generate_image_ultra, generate_image_core, generate_image_sd3
from app.schemas.image import ImageGenerationRequest

logger = logging.getLogger(__name__)


class AvatarService:
    """Service for generating realistic AI avatars for buyer personas."""

    def __init__(self):
        """Initialize the avatar service."""
        self.gemini_model = None
        if settings.GEMINI_API_KEY:
            try:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("Gemini AI initialized successfully for avatar generation")
            except Exception as e:
                logger.error(f"Failed to initialize Gemini AI: {e}")
                self.gemini_model = None
        else:
            logger.warning("GEMINI_API_KEY not found. Avatar generation will use placeholder images.")

    async def generate_avatar(
        self,
        persona_description: str,
        style: str = "professional",
        gender: str = "neutral",
        age: int = 35,
        ethnicity: str = "diverse"
    ) -> Dict[str, Any]:
        """
        Generate an AI avatar for a buyer persona.

        Args:
            persona_description: Description of the persona
            style: Avatar style (professional, casual, creative, executive)
            gender: Gender preference (male, female, neutral)
            age: Age of the persona
            ethnicity: Ethnicity preference (diverse, caucasian, hispanic, asian, african, etc.)

        Returns:
            Dictionary with avatar data and metadata
        """
        try:
            # Generate avatar description for AI
            avatar_prompt = self._create_avatar_prompt(
                persona_description, style, gender, age, ethnicity
            )

            # Generate REAL avatar using Stability AI
            avatar_data = await self._generate_real_avatar_with_stability(
                avatar_prompt, style, gender, age, ethnicity
            )

            return {
                "status": "success",
                "avatar_id": str(uuid.uuid4()),
                "avatar_url": avatar_data["url"],
                "avatar_base64": avatar_data["base64"],
                "style": style,
                "characteristics": {
                    "gender": gender,
                    "age": age,
                    "ethnicity": ethnicity,
                    "style": style,
                    "description": avatar_prompt
                },
                "metadata": {
                    "generated_at": avatar_data["timestamp"],
                    "resolution": "512x512",
                    "format": "PNG"
                }
            }

        except Exception as e:
            logger.error(f"Error generating avatar: {e}")
            return {
                "status": "error",
                "error_message": f"Failed to generate avatar: {str(e)}",
                "avatar_id": None,
                "avatar_url": None
            }

    def _create_avatar_prompt(
        self,
        persona_description: str,
        style: str,
        gender: str,
        age: int,
        ethnicity: str
    ) -> str:
        """Create a detailed prompt for avatar generation."""

        style_descriptions = {
            "professional": "wearing business attire, confident expression, office background",
            "casual": "wearing casual clothes, friendly smile, relaxed environment",
            "creative": "artistic style, unique fashion, creative workspace background",
            "executive": "formal business suit, authoritative presence, corporate setting"
        }

        age_descriptions = {
            (18, 25): "young adult, fresh-faced, energetic appearance",
            (26, 35): "young professional, confident, modern style",
            (36, 45): "experienced professional, mature, established look",
            (46, 55): "senior professional, distinguished, authoritative",
            (56, 65): "executive level, wise appearance, premium style"
        }

        # Determine age description
        age_desc = "professional adult"
        for age_range, desc in age_descriptions.items():
            if age_range[0] <= age <= age_range[1]:
                age_desc = desc
                break

        prompt = f"""
        Create a realistic professional headshot of a {age}-year-old {gender} person.

        Characteristics:
        - Age: {age} years old ({age_desc})
        - Ethnicity: {ethnicity}
        - Style: {style_descriptions.get(style, 'professional')}
        - Expression: Confident and approachable

        Context from persona:
        {persona_description[:200]}...

        Technical requirements:
        - High quality, professional photography style
        - Good lighting, sharp focus
        - Neutral background or relevant to style
        - 512x512 resolution
        - Realistic, not cartoon or illustration
        """

        return prompt.strip()

    async def _generate_real_avatar_with_stability(
        self,
        prompt: str,
        style: str,
        gender: str,
        age: int,
        ethnicity: str
    ) -> Dict[str, Any]:
        """Generate a real avatar using Stability AI."""

        try:
            # Crear prompt optimizado para Stability AI
            stability_prompt = self._create_stability_avatar_prompt(prompt, style, gender, age, ethnicity)

            # Crear request para Stability AI
            image_request = ImageGenerationRequest(
                prompt=stability_prompt,
                width=512,
                height=512,
                model="sd3"  # Usar SD3 primero (MÁS BARATO - ~4 créditos)
            )

            # Generar imagen con Stability AI SD3 (más barato)
            logger.info(f"Generating avatar with Stability AI SD3 (cheapest): {stability_prompt[:100]}...")
            response = await generate_image_sd3(image_request)

            if response.error:
                raise Exception(f"Stability AI SD3 error: {response.error}")

            # La imagen viene en base64
            image_base64 = response.image

            # Crear data URL (SD3 usa PNG)
            data_url = f"data:image/png;base64,{image_base64}"

            return {
                "url": data_url,
                "base64": image_base64,
                "timestamp": "2024-01-01T00:00:00Z",
                "engine": "stability-ai-sd3-turbo",
                "prompt_used": stability_prompt,
                "cost_level": "cheapest"
            }

        except Exception as e:
            logger.error(f"Error generating avatar with SD3 (cheapest): {e}")
            # Fallback a Core si SD3 falla (medio costo - ~6-8 créditos)
            try:
                logger.info("Falling back to Stability AI Core (medium cost)...")
                image_request.model = "core"
                response = await generate_image_core(image_request)

                if response.error:
                    raise Exception(f"Stability AI Core error: {response.error}")

                image_base64 = response.image
                data_url = f"data:image/webp;base64,{image_base64}"

                return {
                    "url": data_url,
                    "base64": image_base64,
                    "timestamp": "2024-01-01T00:00:00Z",
                    "engine": "stability-ai-core",
                    "prompt_used": stability_prompt,
                    "cost_level": "medium"
                }

            except Exception as core_error:
                logger.error(f"Stability AI Core fallback also failed: {core_error}")
                # Último fallback a Ultra (más caro - ~25-30 créditos) solo si es necesario
                try:
                    logger.info("Last resort: Falling back to Stability AI Ultra (most expensive)...")
                    image_request.model = "ultra"
                    response = await generate_image_ultra(image_request)

                    if response.error:
                        raise Exception(f"Stability AI Ultra error: {response.error}")

                    image_base64 = response.image
                    data_url = f"data:image/webp;base64,{image_base64}"

                    return {
                        "url": data_url,
                        "base64": image_base64,
                        "timestamp": "2024-01-01T00:00:00Z",
                        "engine": "stability-ai-ultra",
                        "prompt_used": stability_prompt,
                        "cost_level": "expensive"
                    }

                except Exception as ultra_error:
                    logger.error(f"All Stability AI models failed: {ultra_error}")
                    # Último fallback a placeholder
                    return await self._generate_placeholder_avatar(prompt, style, gender, age, ethnicity)

    def _create_stability_avatar_prompt(
        self,
        base_prompt: str,
        style: str,
        gender: str,
        age: int,
        ethnicity: str
    ) -> str:
        """Create an optimized prompt for Stability AI avatar generation."""

        # Mapear estilos a descripciones específicas para Stability AI
        style_prompts = {
            "professional": "professional business portrait, corporate headshot, clean background, business attire, confident expression, high-quality photography, studio lighting",
            "casual": "casual portrait, friendly smile, relaxed clothing, natural lighting, approachable expression, lifestyle photography",
            "creative": "artistic portrait, creative professional, unique style, artistic lighting, expressive, contemporary fashion",
            "executive": "executive portrait, formal business suit, authoritative presence, premium corporate photography, sophisticated lighting"
        }

        # Mapear etnias a descripciones apropiadas
        ethnicity_prompts = {
            "caucasian": "caucasian person",
            "hispanic": "hispanic person, latino features",
            "asian": "asian person, east asian features",
            "african": "african person, african features",
            "middle_eastern": "middle eastern person",
            "diverse": "diverse ethnicity person"
        }

        # Mapear género
        gender_prompts = {
            "male": "male person, man",
            "female": "female person, woman",
            "neutral": "person"
        }

        # Mapear edad
        age_description = ""
        if age < 25:
            age_description = "young adult"
        elif age < 35:
            age_description = "young professional"
        elif age < 45:
            age_description = "experienced professional"
        elif age < 55:
            age_description = "senior professional"
        else:
            age_description = "executive level professional"

        # Construir prompt optimizado
        prompt_parts = [
            "professional headshot portrait photograph of",
            f"{age_description}",
            f"{ethnicity_prompts.get(ethnicity, 'person')}",
            f"{gender_prompts.get(gender, 'person')}",
            f"age {age}",
            style_prompts.get(style, style_prompts["professional"]),
            "high resolution, sharp focus, professional photography, 8k quality",
            "clean composition, centered subject, neutral expression",
            "realistic, photorealistic, detailed facial features"
        ]

        # Agregar negative prompt implícito en el prompt principal
        negative_elements = "no cartoon, no anime, no illustration, no painting, no artwork, no distorted features"

        final_prompt = ", ".join(prompt_parts) + f", {negative_elements}"

        # Limitar longitud del prompt
        if len(final_prompt) > 1000:
            final_prompt = final_prompt[:1000]

        return final_prompt

    async def _generate_placeholder_avatar(
        self,
        prompt: str,
        style: str,
        gender: str,
        age: int,
        ethnicity: str
    ) -> Dict[str, Any]:
        """Generate a placeholder avatar with AI-generated characteristics."""

        # Create a placeholder image with generated characteristics
        img = Image.new('RGB', (512, 512), color='#f0f0f0')
        draw = ImageDraw.Draw(img)

        # Try to load a font, fallback to default if not available
        try:
            font_large = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 24)
            font_medium = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 18)
            font_small = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 14)
        except:
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
            font_small = ImageFont.load_default()

        # Draw avatar placeholder
        # Background gradient
        for y in range(512):
            color_value = int(240 - (y * 40 / 512))
            draw.line([(0, y), (512, y)], fill=(color_value, color_value, color_value + 10))

        # Draw a simple avatar representation
        # Head circle
        head_color = self._get_skin_tone(ethnicity)
        draw.ellipse([156, 100, 356, 300], fill=head_color, outline='#333333', width=2)

        # Eyes
        draw.ellipse([180, 160, 200, 180], fill='white', outline='#333333')
        draw.ellipse([312, 160, 332, 180], fill='white', outline='#333333')
        draw.ellipse([185, 165, 195, 175], fill='#333333')
        draw.ellipse([317, 165, 327, 175], fill='#333333')

        # Nose
        draw.line([(256, 190), (256, 210)], fill='#333333', width=2)

        # Mouth
        draw.arc([236, 220, 276, 240], 0, 180, fill='#333333', width=2)

        # Hair
        hair_color = '#8B4513' if ethnicity in ['hispanic', 'diverse'] else '#333333'
        draw.ellipse([146, 90, 366, 200], fill=hair_color, outline=hair_color)
        draw.ellipse([156, 100, 356, 190], fill=head_color, outline=head_color)

        # Style-specific clothing
        clothing_color = self._get_clothing_color(style)
        draw.rectangle([156, 300, 356, 512], fill=clothing_color, outline='#333333', width=2)

        # Add text information
        y_offset = 320
        texts = [
            f"AI Avatar",
            f"Age: {age}",
            f"Style: {style.title()}",
            f"Gender: {gender.title()}",
            f"Ethnicity: {ethnicity.title()}"
        ]

        for i, text in enumerate(texts):
            font = font_large if i == 0 else font_small
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            x = (512 - text_width) // 2
            draw.text((x, y_offset + i * 25), text, fill='white', font=font)

        # Convert to base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_base64 = base64.b64encode(buffer.getvalue()).decode()

        # Create data URL
        data_url = f"data:image/png;base64,{img_base64}"

        return {
            "url": data_url,
            "base64": img_base64,
            "timestamp": "2024-01-01T00:00:00Z"
        }

    def _get_skin_tone(self, ethnicity: str) -> tuple:
        """Get appropriate skin tone color for ethnicity."""
        skin_tones = {
            'caucasian': (255, 220, 177),
            'hispanic': (222, 188, 153),
            'asian': (255, 205, 148),
            'african': (139, 90, 43),
            'middle_eastern': (205, 164, 116),
            'diverse': (222, 188, 153),  # Default to medium tone
            'neutral': (222, 188, 153)
        }
        return skin_tones.get(ethnicity.lower(), skin_tones['diverse'])

    def _get_clothing_color(self, style: str) -> tuple:
        """Get appropriate clothing color for style."""
        clothing_colors = {
            'professional': (25, 25, 112),  # Navy blue
            'casual': (70, 130, 180),       # Steel blue
            'creative': (138, 43, 226),     # Blue violet
            'executive': (0, 0, 0)          # Black
        }
        return clothing_colors.get(style.lower(), clothing_colors['professional'])

    async def get_avatar_styles(self) -> List[Dict[str, Any]]:
        """Get available avatar styles."""
        return [
            {
                "id": "professional",
                "name": "Profesional",
                "description": "Traje de negocios, expresión confiada, fondo de oficina",
                "preview_url": "/api/avatars/preview/professional"
            },
            {
                "id": "casual",
                "name": "Casual",
                "description": "Ropa casual, sonrisa amigable, ambiente relajado",
                "preview_url": "/api/avatars/preview/casual"
            },
            {
                "id": "creative",
                "name": "Creativo",
                "description": "Estilo artístico, moda única, espacio de trabajo creativo",
                "preview_url": "/api/avatars/preview/creative"
            },
            {
                "id": "executive",
                "name": "Ejecutivo",
                "description": "Traje formal, presencia autoritaria, entorno corporativo",
                "preview_url": "/api/avatars/preview/executive"
            }
        ]

    async def generate_avatar_gallery(
        self,
        persona_description: str,
        count: int = 4
    ) -> List[Dict[str, Any]]:
        """Generate a gallery of avatar options for a persona using Stability AI."""
        styles = ["professional", "casual", "creative", "executive"]
        genders = ["male", "female", "neutral"]
        ethnicities = ["diverse", "caucasian", "hispanic", "asian", "african"]

        gallery = []

        # Generar variaciones más diversas
        variations = [
            {"style": "professional", "gender": "neutral", "ethnicity": "diverse", "age": 35},
            {"style": "casual", "gender": "male", "ethnicity": "caucasian", "age": 30},
            {"style": "creative", "gender": "female", "ethnicity": "hispanic", "age": 28},
            {"style": "executive", "gender": "male", "ethnicity": "asian", "age": 45},
            {"style": "professional", "gender": "female", "ethnicity": "african", "age": 38},
            {"style": "casual", "gender": "neutral", "ethnicity": "diverse", "age": 32},
        ]

        for i in range(min(count, len(variations))):
            variation = variations[i]

            try:
                avatar = await self.generate_avatar(
                    persona_description=persona_description,
                    style=variation["style"],
                    gender=variation["gender"],
                    age=variation["age"],
                    ethnicity=variation["ethnicity"]
                )

                if avatar["status"] == "success":
                    gallery.append(avatar)

            except Exception as e:
                logger.error(f"Error generating avatar {i+1} in gallery: {e}")
                # Continuar con el siguiente avatar en caso de error
                continue

        return gallery


# Create singleton instance
avatar_service = AvatarService()

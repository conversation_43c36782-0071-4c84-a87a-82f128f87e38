"""
Service for 3D generation using Stability AI.
"""

import base64
import httpx
import logging
from fastapi import HTTP<PERSON>xception, UploadFile

from app.core.config import settings
from app.schemas.generate_3d import Generate3DRequest, Generate3DResponse, Model3DType

logger = logging.getLogger(__name__)


async def generate_3d_model_stability(
    image_file: UploadFile,
    request: Generate3DRequest
) -> Generate3DResponse:
    """
    Generate a 3D model from an image using Stability AI's Stable Fast 3D API.
    
    Args:
        image_file: The input image file
        request: Generation parameters
        
    Returns:
        Generate3DResponse with the GLB model data
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # Determinar URL según el tipo de modelo
        if request.model_type == Model3DType.POINT_AWARE:
            url = f"{settings.STABILITY_API_URL}/v2beta/3d/stable-point-aware-3d"
            credits_used = 4
            model_name = "Stable Point Aware 3D"
        else:
            url = f"{settings.STABILITY_API_URL}/v2beta/3d/stable-fast-3d"
            credits_used = 2
            model_name = "Stable Fast 3D"
        
        # Headers
        headers = {
            "Authorization": f"Bearer {settings.STABILITY_API_KEY}"
        }

        # Leer contenido del archivo
        image_content = await image_file.read()
        
        # Preparar form data
        form_data = {}

        # Parámetros comunes
        if request.texture_resolution:
            form_data["texture_resolution"] = request.texture_resolution.value

        if request.foreground_ratio is not None:
            # Validar rango según el modelo
            if request.model_type == Model3DType.POINT_AWARE:
                # Point Aware: 1.0-2.0, default 1.3
                if request.foreground_ratio < 1.0 or request.foreground_ratio > 2.0:
                    form_data["foreground_ratio"] = "1.3"
                else:
                    form_data["foreground_ratio"] = str(request.foreground_ratio)
            else:
                # Fast 3D: 0.1-1.0, default 0.85
                if request.foreground_ratio < 0.1 or request.foreground_ratio > 1.0:
                    form_data["foreground_ratio"] = "0.85"
                else:
                    form_data["foreground_ratio"] = str(request.foreground_ratio)

        if request.remesh:
            form_data["remesh"] = request.remesh.value

        # Parámetros específicos por modelo
        if request.model_type == Model3DType.POINT_AWARE:
            # Point Aware 3D específicos
            if request.target_type and request.target_type != "none":
                form_data["target_type"] = request.target_type.value
                if request.target_count is not None:
                    form_data["target_count"] = str(request.target_count)

            if request.guidance_scale is not None:
                form_data["guidance_scale"] = str(request.guidance_scale)

            if request.seed is not None and request.seed > 0:
                form_data["seed"] = str(request.seed)
        else:
            # Fast 3D específicos
            if request.vertex_count is not None and request.vertex_count != -1:
                form_data["vertex_count"] = str(request.vertex_count)

        # Preparar archivos
        files = {
            "image": (image_file.filename, image_content, image_file.content_type)
        }

        logger.info(f"Calling Stability AI 3D generation API with params: {form_data}")

        async with httpx.AsyncClient(timeout=180.0) as client:  # 3 minutos timeout
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )

            logger.info(f"Stability AI 3D response status: {response.status_code}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI 3D error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI 3D error: {error_text}"
                )

            # La respuesta es un archivo binario GLB
            model_content = response.content
            
            if not model_content:
                raise ValueError("No model data in response")

            # Codificar en base64 para el transporte
            model_base64 = base64.b64encode(model_content).decode('utf-8')
            
            # Generar nombre de archivo
            base_filename = image_file.filename.rsplit('.', 1)[0] if image_file.filename else "model"
            filename = f"{base_filename}_3d.glb"

            logger.info(f"3D model generated successfully. Size: {len(model_content)} bytes")

            # Preparar metadata según el modelo
            metadata = {
                "model_type": request.model_type.value if request.model_type else "fast",
                "model_name": model_name,
                "credits_used": credits_used,
                "texture_resolution": request.texture_resolution.value if request.texture_resolution else "1024",
                "foreground_ratio": request.foreground_ratio,
                "remesh": request.remesh.value if request.remesh else "none",
                "original_filename": image_file.filename,
                "content_type": "model/gltf-binary"
            }

            # Agregar parámetros específicos del modelo
            if request.model_type == Model3DType.POINT_AWARE:
                metadata.update({
                    "target_type": request.target_type.value if request.target_type else "none",
                    "target_count": request.target_count,
                    "guidance_scale": request.guidance_scale,
                    "seed": request.seed
                })
            else:
                metadata["vertex_count"] = request.vertex_count

            return Generate3DResponse(
                success=True,
                model_data=model_base64,
                filename=filename,
                size_bytes=len(model_content),
                metadata=metadata
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in 3D generation: {e}", exc_info=True)
        return Generate3DResponse(
            success=False,
            error=f"Error generating 3D model: {str(e)}"
        )

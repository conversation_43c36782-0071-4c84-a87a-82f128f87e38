"""
Service for GPT-4 image generation using the gpt-image-1 model.
This service uses the correct OpenAI client pattern with images.generate().
"""

import logging
import base64
import io
from typing import Optional, List
from openai import OpenAI
from app.core.config import settings
from app.schemas.image import GPTImageGenerationRequest, GPTImageGenerationResponse

logger = logging.getLogger(__name__)


class GPTImageService:
    """Service for generating images using gpt-image-1 model via OpenAI client."""

    def __init__(self):
        self.api_key = settings.OPENAI_API_KEY
        self.client = OpenAI(api_key=self.api_key) if self.api_key else None
        # gpt-image-1 takes longer to generate, so increase timeout
        self.timeout = 300.0  # 5 minutes



    async def generate_image(self, request: GPTImageGenerationRequest) -> GPTImageGenerationResponse:
        """
        Generate an image using gpt-image-1 model via OpenAI client.

        Args:
            request: The image generation request

        Returns:
            GPTImageGenerationResponse with the generated image
        """
        if not self.client:
            logger.error("OpenAI client not configured - API key missing")
            return GPTImageGenerationResponse(
                success=False,
                error="OpenAI API key not configured"
            )

        try:
            logger.info(f"🎨 Generating image with gpt-image-1: {request.prompt[:100]}...")

            # Use the correct OpenAI client pattern for gpt-image-1
            # Note: gpt-image-1 may not support all parameters that DALL-E models support
            result = self.client.images.generate(
                model="gpt-image-1",
                prompt=request.prompt,
                size=request.size
                # Note: quality and response_format may not be supported by gpt-image-1
            )

            logger.info("✅ gpt-image-1 generation completed successfully")

            # Extract image data (URL or base64)
            if result.data and len(result.data) > 0:
                image_data = result.data[0]

                # gpt-image-1 can return either URL or base64
                final_image_url = None

                if hasattr(image_data, 'url') and image_data.url:
                    # Direct URL from OpenAI
                    final_image_url = image_data.url
                    logger.info("✅ Successfully received image URL from gpt-image-1")
                elif hasattr(image_data, 'b64_json') and image_data.b64_json:
                    # Base64 data - convert to data URL
                    final_image_url = f"data:image/png;base64,{image_data.b64_json}"
                    logger.info("✅ Successfully converted base64 image to data URL")

                if final_image_url:
                    return GPTImageGenerationResponse(
                        success=True,
                        image_url=final_image_url,
                        revised_prompt=getattr(image_data, 'revised_prompt', None),
                        metadata={
                            "model": "gpt-image-1",
                            "size": request.size,
                            "quality": getattr(request, 'quality', 'standard'),
                            "original_prompt": request.prompt,
                            "generation_time": "extended",
                            "endpoint": "images/generate"
                        }
                    )
                else:
                    logger.error("No URL or base64 image data in gpt-image-1 response")
                    return GPTImageGenerationResponse(
                        success=False,
                        error="No image data in gpt-image-1 response"
                    )
            else:
                logger.error("Empty response data from gpt-image-1")
                return GPTImageGenerationResponse(
                    success=False,
                    error="Empty response from gpt-image-1"
                )

        except Exception as e:
            error_message = str(e)
            logger.error(f"Error generating image with gpt-image-1: {error_message}")

            # Handle specific billing errors
            if "billing" in error_message.lower() or "limit" in error_message.lower():
                return GPTImageGenerationResponse(
                    success=False,
                    error="OpenAI billing limit reached. Please check your account or try again later."
                )

            return GPTImageGenerationResponse(
                success=False,
                error=f"gpt-image-1 generation failed: {error_message}"
            )


# Global service instance
gpt_image_service = GPTImageService()


async def generate_gpt_image(request: GPTImageGenerationRequest) -> GPTImageGenerationResponse:
    """
    Generate an image using gpt-image-1 model via OpenAI client.

    This model is designed for:
    - High-quality image generation
    - Text-based image creation
    - Professional social media content
    - Brand-focused visual content

    Note: gpt-image-1 takes longer to generate images than standard DALL-E models.

    Args:
        request: The image generation request

    Returns:
        GPTImageGenerationResponse with the generated image
    """
    return await gpt_image_service.generate_image(request)

"""Main buyer persona service orchestrating specialized services."""

import logging

from app.schemas.buyer_persona import (
    BuyerPersonaRequest,
    BuyerPersonaResponse,
    TextAssistRequest,
    TextAssistResponse
)
from app.services.persona_generation_service import PersonaGenerationService
from app.services.text_assistance_service import TextAssistanceService
from app.services.ai_provider_service import AIProviderService

logger = logging.getLogger(__name__)


class BuyerPersonaService:
    """Main service for buyer persona operations following SOLID principles."""

    def __init__(self):
        """Initialize the service with specialized components."""
        self.persona_generator = PersonaGenerationService()
        self.text_assistant = TextAssistanceService()
        self.ai_provider = AIProviderService()

    async def generate_buyer_personas(self, request: BuyerPersonaRequest) -> BuyerPersonaResponse:
        """Generate buyer personas using the specialized generation service."""
        return await self.persona_generator.generate_buyer_personas(request)

    async def improve_text(self, request: TextAssistRequest) -> TextAssistResponse:
        """Improve text using the specialized text assistance service."""
        return await self.text_assistant.improve_text(request)

    def get_service_status(self) -> dict:
        """Get the status of the buyer persona service."""
        return {
            "ai_available": self.ai_provider.is_ai_available(),
            "model_name": self.ai_provider.get_model_name(),
            "services": {
                "persona_generation": "available",
                "text_assistance": "available"
            }
        }


# Create singleton instance
buyer_persona_service = BuyerPersonaService()
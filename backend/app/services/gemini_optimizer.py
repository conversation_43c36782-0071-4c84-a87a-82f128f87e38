"""
Gemini Optimizer Service
Handles content optimization using Google's Gemini AI for SEO and LLM optimization
"""

import logging
import asyncio
import json
import re
from typing import Dict, Any, List, Optional

import google.generativeai as genai
from app.core.config import get_settings

logger = logging.getLogger(__name__)

class GeminiOptimizer:
    """Service for optimizing content using Gemini AI"""
    
    def __init__(self):
        self.settings = get_settings()
        self.api_key = getattr(self.settings, 'GEMINI_API_KEY', None)
        
        if not self.api_key:
            raise Exception("GEMINI_API_KEY not configured. Please set your Gemini API key.")

        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-1.5-pro')
    
    async def analyze_content_strategy(
        self, 
        keyword: str, 
        serp_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Analyze SERP data and generate content strategy using Gemini
        
        Args:
            keyword: Target keyword
            serp_data: SERP analysis data
            
        Returns:
            Content strategy recommendations
        """
        try:
            # Prepare prompt for Gemini
            prompt = self._build_content_strategy_prompt(keyword, serp_data)

            # Generate response
            response = await self._generate_response(prompt)

            # Parse and structure response
            strategy = await self._parse_content_strategy(response, keyword)

            return strategy

        except Exception as e:
            logger.error(f"Error analyzing content strategy: {str(e)}")
            raise Exception(f"Content strategy analysis failed: {str(e)}")
    
    async def optimize_content(
        self, 
        keyword: str, 
        content: str, 
        target_length: int = 1500
    ) -> Dict[str, Any]:
        """
        Optimize existing content for SEO and LLM performance
        
        Args:
            keyword: Target keyword
            content: Content to optimize
            target_length: Target content length
            
        Returns:
            Optimization recommendations
        """
        try:
            prompt = self._build_optimization_prompt(keyword, content, target_length)
            response = await self._generate_response(prompt)
            optimization = await self._parse_optimization_response(response)

            return optimization

        except Exception as e:
            logger.error(f"Error optimizing content: {str(e)}")
            raise Exception(f"Content optimization failed: {str(e)}")
    
    async def generate_schema(
        self, 
        content: str, 
        content_type: str = "Article"
    ) -> Dict[str, Any]:
        """
        Generate Schema.org markup for content
        
        Args:
            content: Content to generate schema for
            content_type: Type of schema (Article, BlogPosting, etc.)
            
        Returns:
            Schema.org JSON-LD and HTML
        """
        try:
            prompt = self._build_schema_prompt(content, content_type)
            response = await self._generate_response(prompt)
            schema = await self._parse_schema_response(response)

            return schema

        except Exception as e:
            logger.error(f"Error generating schema: {str(e)}")
            raise Exception(f"Schema generation failed: {str(e)}")
    
    async def calculate_seo_score(
        self, 
        serp_data: Dict[str, Any], 
        content_analysis: Dict[str, Any]
    ) -> int:
        """Calculate SEO score based on SERP and content analysis"""
        
        score = 70  # Base score
        
        # Adjust based on competitor analysis
        if serp_data.get("total_results", 0) > 5:
            score += 10
        
        # Adjust based on content strategy completeness
        if len(content_analysis.get("top_topics", [])) >= 5:
            score += 10
        
        if len(content_analysis.get("structure", [])) >= 6:
            score += 5
        
        if len(content_analysis.get("faqs", [])) >= 3:
            score += 5
        
        return min(score, 100)
    
    async def calculate_llm_score(self, content_analysis: Dict[str, Any]) -> int:
        """Calculate LLM optimization score"""
        
        score = 80  # Base score
        
        # Adjust based on structure clarity
        if len(content_analysis.get("structure", [])) >= 8:
            score += 10
        
        # Adjust based on FAQ coverage
        if len(content_analysis.get("faqs", [])) >= 5:
            score += 5
        
        # Adjust based on entity coverage
        if len(content_analysis.get("entities", [])) >= 6:
            score += 5
        
        return min(score, 100)
    
    def _build_content_strategy_prompt(self, keyword: str, serp_data: Dict[str, Any]) -> str:
        """Build prompt for content strategy analysis"""
        
        competitors = serp_data.get("raw_results", [])[:3]
        common_keywords = serp_data.get("common_keywords", [])[:10]
        
        prompt = f"""
Analiza la keyword "{keyword}" y los siguientes resultados top de Google para crear una estrategia de contenido optimizada tanto para SEO como para LLMs (ChatGPT, Gemini).

COMPETIDORES TOP:
{json.dumps(competitors, indent=2, ensure_ascii=False)}

PALABRAS CLAVE COMUNES:
{', '.join(common_keywords)}

Devuelve un JSON con esta estructura exacta:
{{
    "top_topics": ["tema 1", "tema 2", "tema 3", "tema 4", "tema 5"],
    "structure": [
        {{"level": "H1", "text": "Título principal", "priority": 1}},
        {{"level": "H2", "text": "Subtítulo importante", "priority": 2}},
        {{"level": "H3", "text": "Subsección", "priority": 3}}
    ],
    "faqs": ["¿Pregunta 1?", "¿Pregunta 2?", "¿Pregunta 3?"],
    "entities": ["entidad 1", "entidad 2", "entidad 3"],
    "optimization_tips": ["tip 1", "tip 2", "tip 3"]
}}

IMPORTANTE: 
- Los temas deben cubrir la intención de búsqueda completa
- La estructura debe ser lógica y fácil de seguir para humanos y LLMs
- Las FAQs deben responder dudas reales de usuarios
- Las entidades deben ser conceptos clave relacionados
- Los tips deben ser accionables y específicos
"""
        return prompt
    
    def _build_optimization_prompt(self, keyword: str, content: str, target_length: int) -> str:
        """Build prompt for content optimization"""
        
        content_preview = content[:1000] + "..." if len(content) > 1000 else content
        
        prompt = f"""
Analiza este contenido para la keyword "{keyword}" y proporciona optimizaciones específicas:

CONTENIDO ACTUAL:
{content_preview}

LONGITUD OBJETIVO: {target_length} palabras
LONGITUD ACTUAL: {len(content.split())} palabras

Devuelve un JSON con esta estructura:
{{
    "seo_score": 85,
    "llm_score": 90,
    "missing_topics": ["tema que falta 1", "tema que falta 2"],
    "suggestions": ["sugerencia específica 1", "sugerencia específica 2"],
    "readability_score": 75,
    "keyword_density": 2.5
}}

Analiza:
1. Cobertura de temas importantes
2. Estructura y legibilidad
3. Densidad de keyword (objetivo: 1-3%)
4. Claridad para LLMs
5. Optimización SEO técnica
"""
        return prompt
    
    def _build_schema_prompt(self, content: str, content_type: str) -> str:
        """Build prompt for schema generation"""
        
        content_preview = content[:500] + "..." if len(content) > 500 else content
        
        prompt = f"""
Genera Schema.org JSON-LD para este contenido tipo "{content_type}":

CONTENIDO:
{content_preview}

Devuelve un JSON con esta estructura:
{{
    "json": {{
        "@context": "https://schema.org",
        "@type": "{content_type}",
        // ... resto del schema
    }},
    "html": "<script type='application/ld+json'>...</script>"
}}

El schema debe incluir:
- Información básica del contenido
- Autor/organización
- Fecha de publicación
- Descripción
- Palabras clave relevantes
- Estructura si es aplicable
"""
        return prompt
    
    async def _generate_response(self, prompt: str) -> str:
        """Generate response from Gemini"""
        try:
            response = await asyncio.to_thread(
                self.model.generate_content, 
                prompt
            )
            return response.text
        except Exception as e:
            logger.error(f"Error generating Gemini response: {str(e)}")
            raise
    
    async def _parse_content_strategy(self, response: str, keyword: str) -> Dict[str, Any]:
        """Parse content strategy response from Gemini"""
        try:
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                raise Exception("No valid JSON found in Gemini response")
        except Exception as e:
            logger.error(f"Error parsing content strategy: {str(e)}")
            raise Exception(f"Failed to parse Gemini response: {str(e)}")
    
    async def _parse_optimization_response(self, response: str) -> Dict[str, Any]:
        """Parse optimization response from Gemini"""
        try:
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                raise Exception("No valid JSON found in Gemini optimization response")
        except Exception as e:
            logger.error(f"Error parsing optimization response: {str(e)}")
            raise Exception(f"Failed to parse optimization response: {str(e)}")
    
    async def _parse_schema_response(self, response: str) -> Dict[str, Any]:
        """Parse schema response from Gemini"""
        try:
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                raise Exception("No valid JSON found in Gemini schema response")
        except Exception as e:
            logger.error(f"Error parsing schema response: {str(e)}")
            raise Exception(f"Failed to parse schema response: {str(e)}")



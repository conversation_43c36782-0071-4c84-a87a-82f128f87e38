"""
Style Transfer service using Stability AI API.
Implements the /v2beta/stable-image/control/style-transfer endpoint.
"""

import io
import httpx
import logging
from fastapi import HTTPException
from app.core.config import settings
from app.schemas.style_transfer import StyleTransferResponse

logger = logging.getLogger("emma_studio")


def translate_prompt_to_english(spanish_prompt: str) -> str:
    """
    Traduce prompts del español al inglés para Stability AI.
    Stability AI solo acepta prompts en inglés.
    """
    if not spanish_prompt or spanish_prompt.strip() == '':
        return spanish_prompt

    # Diccionario básico de traducciones comunes para prompts de IA
    translations = {
        # Términos básicos
        'mantén': 'keep',
        'mantenlo': 'keep it',
        'mantener': 'maintain',
        'igual': 'same',
        'solo': 'only',
        'que': 'that',
        'en': 'in',
        'el': 'the',
        'la': 'the',
        'nuevo': 'new',
        'estilo': 'style',
        'mujer': 'woman',
        'hombre': 'man',
        'persona': 'person',
        'tal': 'such',
        'como': 'as',
        'está': 'is',
        'elegante': 'elegant',
        'rojo': 'red',
        'de': 'of',
        'lava': 'lava',

        # Términos de calidad
        'borroso': 'blurry',
        'baja calidad': 'low quality',
        'distorsionado': 'distorted',
        'alta calidad': 'high quality',
        'profesional': 'professional',
        'detallado': 'detailed',
        'realista': 'realistic',
        'artístico': 'artistic',
        'vibrante': 'vibrant',
        'suave': 'soft',
        'nítido': 'sharp',

        # Colores
        'azul': 'blue',
        'verde': 'green',
        'amarillo': 'yellow',
        'negro': 'black',
        'blanco': 'white',
        'gris': 'gray',
        'rosa': 'pink',
        'morado': 'purple',
        'naranja': 'orange',
        'marrón': 'brown',
    }

    # Traducciones de frases completas comunes
    phrase_translations = {
        'mantenlo igual solo que en el nuevo estilo': 'keep it the same but in the new style',
        'mantén la mujer tal como está': 'keep the woman as she is',
        'rojo de lava': 'lava red',
        'borroso, de baja calidad, distorsionado': 'blurry, low quality, distorted',
        'alta calidad': 'high quality',
        'estilo elegante': 'elegant style',
    }

    translated_prompt = spanish_prompt.lower()

    # Aplicar traducciones de frases completas primero
    for spanish_phrase, english_phrase in phrase_translations.items():
        if spanish_phrase in translated_prompt:
            translated_prompt = translated_prompt.replace(spanish_phrase, english_phrase)

    # Luego aplicar traducciones palabra por palabra
    for spanish_word, english_word in translations.items():
        # Usar word boundaries para evitar traducciones parciales
        import re
        pattern = r'\b' + re.escape(spanish_word) + r'\b'
        translated_prompt = re.sub(pattern, english_word, translated_prompt, flags=re.IGNORECASE)

    # Limpiar espacios múltiples y capitalizar primera letra
    translated_prompt = re.sub(r'\s+', ' ', translated_prompt).strip()
    if translated_prompt:
        translated_prompt = translated_prompt[0].upper() + translated_prompt[1:]

    logger.info(f"🌐 Prompt traducido: '{spanish_prompt}' → '{translated_prompt}'")
    return translated_prompt


async def call_stability_style_transfer(
    init_image_content: bytes,
    style_image_content: bytes,
    prompt: str = "",
    negative_prompt: str = None,
    style_strength: float = 1.0,
    composition_fidelity: float = 0.9,
    change_strength: float = 0.9,
    seed: int = 0,
    output_format: str = "png"
) -> StyleTransferResponse:
    """
    Apply style transfer using Stability AI v2beta API.
    
    Args:
        init_image_content: The original image to be restyled
        style_image_content: The style reference image
        prompt: Optional text prompt to guide the generation
        negative_prompt: What you don't want to see
        style_strength: How much influence the style image has (0.0-1.0)
        composition_fidelity: How closely output resembles input style (0.0-1.0)
        change_strength: How much the original image should change (0.1-1.0)
        seed: Random seed for reproducible results
        output_format: Output image format (jpeg, png, webp)
    
    Returns:
        StyleTransferResponse with base64 encoded image
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # URL de la API v2beta para style transfer
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/control/style-transfer"

        # Headers para recibir respuesta JSON con base64
        headers = {
            "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
            "Accept": "application/json"
        }

        # Traducir prompts al inglés antes de enviar a Stability AI
        translated_prompt = ""
        translated_negative_prompt = ""

        if prompt and prompt.strip():
            translated_prompt = translate_prompt_to_english(prompt.strip())

        if negative_prompt and negative_prompt.strip():
            translated_negative_prompt = translate_prompt_to_english(negative_prompt.strip())

        # Preparar FormData
        form_data = {
            "output_format": output_format,
            "style_strength": str(style_strength),
            "composition_fidelity": str(composition_fidelity),
            "change_strength": str(change_strength),
            "seed": str(seed)
        }

        # Agregar campos opcionales (ya traducidos)
        if translated_prompt:
            form_data["prompt"] = translated_prompt
        if translated_negative_prompt:
            form_data["negative_prompt"] = translated_negative_prompt

        # Preparar los archivos para upload
        files = {
            "init_image": ("init_image.jpg", io.BytesIO(init_image_content), "image/jpeg"),
            "style_image": ("style_image.jpg", io.BytesIO(style_image_content), "image/jpeg")
        }

        logger.info(f"Calling Stability AI style transfer with translated parameters: {form_data}")

        async with httpx.AsyncClient(timeout=180.0) as client:  # 3 minutos timeout
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )

            logger.info(f"Stability AI response status: {response.status_code}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI error: {error_text}"
                )

            # Procesar respuesta JSON
            result = response.json()
            image_data = result.get("image")

            if not image_data:
                raise ValueError("No image data in response")

            return StyleTransferResponse(
                image=image_data,
                seed=result.get("seed"),
                finish_reason=result.get("finish_reason", "SUCCESS")
            )

    except httpx.TimeoutException:
        logger.error("Timeout calling Stability AI style transfer API")
        raise HTTPException(
            status_code=504,
            detail="Timeout processing style transfer request"
        )
    except Exception as e:
        logger.error(f"Error in style transfer service: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal error: {str(e)}"
        )

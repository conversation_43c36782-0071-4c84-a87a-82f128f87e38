"""Service for simulating conversations with buyer personas."""

import logging
import json
import os
import uuid
import random
from typing import Dict, Any, List, Optional
from datetime import datetime
import google.generativeai as genai
from app.core.config import settings

logger = logging.getLogger(__name__)

# Nombres realistas por industria y tipo
REALISTIC_NAMES = {
    "tech": {
        "male": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
        "female": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"]
    },
    "business": {
        "male": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
        "female": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]
    },
    "creative": {
        "male": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"],
        "female": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]
    },
    "health": {
        "male": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>. <PERSON>"],
        "female": ["<PERSON><PERSON>. <PERSON>", "Dr<PERSON>. <PERSON>", "Dra. <PERSON>", "Dra. <PERSON><PERSON>"]
    },
    "education": {
        "male": ["Prof. <PERSON>don<PERSON>", "Prof. Ra<PERSON> <PERSON>ab<PERSON>", "Prof. <PERSON>r <PERSON>", "Prof. I<PERSON>"],
        "female": ["Prof. <PERSON>s", "Prof. <PERSON>er<PERSON>ica <PERSON>", "Prof. Pa<PERSON> Contre<PERSON>", "Prof. Silvia Mora"]
    },
    "default": {
        "male": ["Juan Carlos López", "Pedro Martínez", "Luis García", "José Rodríguez", "Manuel González"],
        "female": ["María José Silva", "Ana Belén Torres", "Carmen López", "Pilar Martín", "Rosa García"]
    }
}

# Mapeo de industrias a categorías de nombres
INDUSTRY_NAME_MAPPING = {
    "tech": "tech",
    "technology": "tech",
    "software": "tech",
    "ecommerce": "business",
    "business": "business",
    "finance": "business",
    "marketing": "business",
    "health": "health",
    "healthcare": "health",
    "medical": "health",
    "education": "education",
    "teaching": "education",
    "training": "education",
    "design": "creative",
    "creative": "creative",
    "art": "creative",
    "media": "creative"
}


class ConversationSimulatorService:
    """Service for simulating realistic conversations with buyer personas."""

    def __init__(self):
        """Initialize the conversation simulator service."""
        self.conversations_dir = "conversations"  # Directory to store conversations
        self._ensure_conversations_directory()
        self.gemini_model = None
        if settings.GEMINI_API_KEY:
            try:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("Gemini AI initialized successfully for conversation simulation")
            except Exception as e:
                logger.error(f"Failed to initialize Gemini AI: {e}")
                self.gemini_model = None
        else:
            logger.warning("GEMINI_API_KEY not found. Conversation simulation will use mock responses.")

    def _generate_realistic_name(self, persona_data: Dict[str, Any]) -> str:
        """Generate a realistic name based on persona data."""
        try:
            # Extraer información relevante
            job_info = persona_data.get("job", {})
            industry = job_info.get("industry", "").lower()
            age = persona_data.get("age", 35)

            # Determinar categoría de nombre basada en industria
            name_category = "default"
            for key, category in INDUSTRY_NAME_MAPPING.items():
                if key in industry:
                    name_category = category
                    break

            # Determinar género basado en edad y contexto
            # Personas más jóvenes tienden a ser más diversas en nombres
            gender = "female" if random.random() > 0.6 else "male"  # 60% female, 40% male

            # Seleccionar nombres de la categoría apropiada
            names_pool = REALISTIC_NAMES.get(name_category, REALISTIC_NAMES["default"])
            available_names = names_pool.get(gender, names_pool["female"])

            # Seleccionar nombre aleatorio
            selected_name = random.choice(available_names)

            logger.info(f"Generated realistic name: {selected_name} for industry: {industry}")
            return selected_name

        except Exception as e:
            logger.error(f"Error generating realistic name: {e}")
            # Fallback a nombre por defecto
            return random.choice(REALISTIC_NAMES["default"]["female"])

    async def start_conversation(
        self,
        persona_data: Dict[str, Any],
        conversation_type: str = "sales",
        context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Start a new conversation simulation with a buyer persona.

        Args:
            persona_data: Complete buyer persona information
            conversation_type: Type of conversation (sales, support, discovery, objection_handling)
            context: Optional context for the conversation

        Returns:
            Conversation session data with initial message
        """
        try:
            conversation_id = str(uuid.uuid4())

            # Generate realistic name if not provided or if it's generic
            if not persona_data.get('name') or persona_data.get('name') in ['Unknown', 'Persona', 'Cliente']:
                persona_data['name'] = self._generate_realistic_name(persona_data)

            # Create persona context for conversation
            persona_context = self._create_persona_context(persona_data, conversation_type, context)

            # Generate initial message from persona (as potential customer)
            initial_message = await self._generate_persona_response(
                persona_context=persona_context,
                conversation_history=[],
                user_message="INICIO_CONVERSACION"  # Special trigger for initial message
            )

            conversation_session = {
                "conversation_id": conversation_id,
                "persona_name": persona_data.get('name', 'Unknown'),
                "conversation_type": conversation_type,
                "status": "active",
                "created_at": datetime.now().isoformat(),
                "context": persona_context,
                "messages": [
                    {
                        "id": str(uuid.uuid4()),
                        "sender": "persona",
                        "message": initial_message,
                        "timestamp": datetime.now().isoformat(),
                        "persona_state": {
                            "interest_level": 50,
                            "trust_level": 30,
                            "urgency_level": 20
                        }
                    }
                ],
                "analytics": {
                    "total_messages": 1,
                    "conversation_score": 50,
                    "key_topics_discussed": [],
                    "objections_raised": [],
                    "buying_signals": []
                }
            }

            # Save conversation to file
            self._save_conversation(conversation_session)

            return {
                "status": "success",
                "conversation": conversation_session
            }

        except Exception as e:
            logger.error(f"Error starting conversation: {e}")
            return {
                "status": "error",
                "error_message": f"Failed to start conversation: {str(e)}"
            }

    async def continue_conversation(
        self,
        conversation_id: str,
        user_message: str,
        conversation_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Continue an existing conversation with a persona response.

        Args:
            conversation_id: ID of the conversation session
            user_message: Message from the user/salesperson
            conversation_data: Current conversation state

        Returns:
            Updated conversation with persona response
        """
        try:
            # Create proper persona context if needed
            if "persona_data" in conversation_data["context"]:
                # Convert persona_data to proper context format
                persona_context = self._create_persona_context(
                    persona_data=conversation_data["context"]["persona_data"],
                    conversation_type="sales",
                    context="Conversación de ventas en curso"
                )
            else:
                persona_context = conversation_data["context"]

            # Generate persona response
            persona_response = await self._generate_persona_response(
                persona_context=persona_context,
                conversation_history=conversation_data["messages"],
                user_message=user_message
            )

            # Analyze conversation state
            conversation_analysis = await self._analyze_conversation_state(
                conversation_data["messages"] + [{"sender": "user", "message": user_message}],
                persona_context
            )

            # Add new messages
            new_user_message = {
                "id": str(uuid.uuid4()),
                "sender": "user",
                "message": user_message,
                "timestamp": datetime.now().isoformat()
            }

            new_persona_message = {
                "id": str(uuid.uuid4()),
                "sender": "persona",
                "message": persona_response,
                "timestamp": datetime.now().isoformat(),
                "persona_state": conversation_analysis["persona_state"]
            }

            # Update conversation
            conversation_data["messages"].extend([new_user_message, new_persona_message])
            conversation_data["analytics"] = conversation_analysis["analytics"]
            conversation_data["analytics"]["total_messages"] = len(conversation_data["messages"])

            # Ensure conversation_id is in the data for saving
            if "conversation_id" not in conversation_data:
                conversation_data["conversation_id"] = conversation_id

            # Save updated conversation to file
            self._save_conversation(conversation_data)

            return {
                "status": "success",
                "conversation": conversation_data,
                "persona_response": persona_response,
                "analysis": conversation_analysis
            }

        except Exception as e:
            logger.error(f"Error continuing conversation: {e}")
            return {
                "status": "error",
                "error_message": f"Failed to continue conversation: {str(e)}"
            }

    def _create_persona_context(
        self,
        persona_data: Dict[str, Any],
        conversation_type: str,
        context: Optional[str]
    ) -> Dict[str, Any]:
        """Create comprehensive context for persona conversation."""

        return {
            "persona_profile": {
                "name": persona_data.get('name', 'Unknown'),
                "age": persona_data.get('age', 35),
                "job_title": persona_data.get('job', {}).get('title', 'Professional'),
                "industry": persona_data.get('job', {}).get('industry', 'Technology'),
                "company_size": persona_data.get('job', {}).get('company_size', 'Medium'),
                "income_level": persona_data.get('income_level', 'Medium'),
                "goals": persona_data.get('goals', []),
                "challenges": persona_data.get('challenges', []),
                "communication_style": self._determine_communication_style(persona_data),
                "personality_traits": self._extract_personality_traits(persona_data),
                "buying_process": persona_data.get('buying_process', {}),
                "objections": persona_data.get('objections', []),
                "influences": persona_data.get('influences', [])
            },
            "conversation_settings": {
                "type": conversation_type,
                "context": context or "Initial product inquiry",
                "persona_mood": "neutral",
                "interest_level": 50,
                "trust_level": 30,
                "urgency_level": 20
            },
            "conversation_rules": self._get_conversation_rules(conversation_type),
            "response_guidelines": self._get_response_guidelines(persona_data, conversation_type)
        }

    def _determine_communication_style(self, persona_data: Dict[str, Any]) -> str:
        """Determine communication style based on persona characteristics."""
        job_title = persona_data.get('job', {}).get('title', '').lower()
        age = persona_data.get('age', 35)

        if 'ceo' in job_title or 'director' in job_title:
            return "direct_executive"
        elif 'technical' in job_title or 'engineer' in job_title:
            return "analytical_technical"
        elif age < 30:
            return "casual_modern"
        elif age > 50:
            return "formal_traditional"
        else:
            return "professional_balanced"

    def _extract_personality_traits(self, persona_data: Dict[str, Any]) -> List[str]:
        """Extract personality traits from persona data."""
        traits = []

        goals = persona_data.get('goals', [])
        challenges = persona_data.get('challenges', [])
        job_title = persona_data.get('job', {}).get('title', '').lower()

        # Analyze goals for traits
        goal_text = ' '.join(goals).lower()
        if 'efficiency' in goal_text or 'optimize' in goal_text:
            traits.append("efficiency_focused")
        if 'growth' in goal_text or 'scale' in goal_text:
            traits.append("growth_oriented")
        if 'innovation' in goal_text or 'new' in goal_text:
            traits.append("innovation_seeking")

        # Analyze challenges for traits
        challenge_text = ' '.join(challenges).lower()
        if 'time' in challenge_text or 'busy' in challenge_text:
            traits.append("time_conscious")
        if 'budget' in challenge_text or 'cost' in challenge_text:
            traits.append("cost_conscious")
        if 'team' in challenge_text or 'people' in challenge_text:
            traits.append("people_focused")

        # Job-based traits
        if 'manager' in job_title or 'director' in job_title:
            traits.append("decision_maker")
        if 'technical' in job_title:
            traits.append("detail_oriented")

        return traits or ["professional", "analytical"]

    def _get_conversation_rules(self, conversation_type: str) -> Dict[str, Any]:
        """Get conversation rules based on type."""
        rules = {
            "sales": {
                "max_response_length": 150,
                "objection_probability": 0.3,
                "buying_signal_probability": 0.2,
                "question_probability": 0.4,
                "follow_up_probability": 0.6
            },
            "discovery": {
                "max_response_length": 200,
                "objection_probability": 0.1,
                "buying_signal_probability": 0.1,
                "question_probability": 0.7,
                "follow_up_probability": 0.8
            },
            "objection_handling": {
                "max_response_length": 100,
                "objection_probability": 0.8,
                "buying_signal_probability": 0.1,
                "question_probability": 0.3,
                "follow_up_probability": 0.4
            },
            "support": {
                "max_response_length": 120,
                "objection_probability": 0.2,
                "buying_signal_probability": 0.0,
                "question_probability": 0.6,
                "follow_up_probability": 0.5
            }
        }

        return rules.get(conversation_type, rules["sales"])

    def _get_response_guidelines(
        self,
        persona_data: Dict[str, Any],
        conversation_type: str
    ) -> Dict[str, Any]:
        """Get response guidelines for the persona."""

        communication_style = self._determine_communication_style(persona_data)

        style_guidelines = {
            "direct_executive": {
                "tone": "direct, time-conscious, results-focused",
                "vocabulary": "business terminology, ROI focus",
                "response_pattern": "brief, to the point, asks about bottom line"
            },
            "analytical_technical": {
                "tone": "detailed, questioning, specification-focused",
                "vocabulary": "technical terms, specific requirements",
                "response_pattern": "asks for technical details, wants proof"
            },
            "casual_modern": {
                "tone": "informal, friendly, open to innovation",
                "vocabulary": "modern terms, casual language",
                "response_pattern": "conversational, asks about user experience"
            },
            "formal_traditional": {
                "tone": "formal, cautious, process-oriented",
                "vocabulary": "traditional business language",
                "response_pattern": "methodical, wants references and proof"
            },
            "professional_balanced": {
                "tone": "professional but approachable",
                "vocabulary": "standard business language",
                "response_pattern": "balanced questions about features and benefits"
            }
        }

        return style_guidelines.get(communication_style, style_guidelines["professional_balanced"])

    async def _generate_persona_response(
        self,
        persona_context: Dict[str, Any],
        conversation_history: List[Dict[str, Any]],
        user_message: str
    ) -> str:
        """Generate a realistic persona response."""

        if self.gemini_model:
            return await self._generate_ai_response(persona_context, conversation_history, user_message)
        else:
            return self._generate_mock_response(persona_context, user_message)

    async def _generate_ai_response(
        self,
        persona_context: Dict[str, Any],
        conversation_history: List[Dict[str, Any]],
        user_message: str
    ) -> str:
        """Generate response using Gemini AI."""

        prompt = self._create_conversation_prompt(persona_context, conversation_history, user_message)

        try:
            response = self.gemini_model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.8,  # Higher temperature for more natural conversation
                    top_p=0.9,
                    top_k=40,
                    max_output_tokens=300,
                )
            )

            # Clean and validate response
            persona_response = response.text.strip()

            # Ensure response isn't too long
            max_length = persona_context["conversation_rules"]["max_response_length"]
            if len(persona_response) > max_length:
                persona_response = persona_response[:max_length] + "..."

            return persona_response

        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            return self._generate_mock_response(persona_context, user_message)

    def _create_conversation_prompt(
        self,
        persona_context: Dict[str, Any],
        conversation_history: List[Dict[str, Any]],
        user_message: str
    ) -> str:
        """Create prompt for conversation generation."""

        persona = persona_context["persona_profile"]
        settings = persona_context["conversation_settings"]
        guidelines = persona_context["response_guidelines"]

        history_text = ""
        if conversation_history:
            recent_history = conversation_history[-6:]  # Last 6 messages for context
            for msg in recent_history:
                sender = "Vendedor" if msg["sender"] == "user" else persona["name"]
                history_text += f"{sender}: {msg['message']}\n"

        # Handle initial conversation message
        if user_message == "INICIO_CONVERSACION":
            prompt = f"""
            Eres {persona["name"]}, un/a {persona["job_title"]} de {persona["age"]} años que trabaja en una empresa {persona["company_size"]} en la industria {persona["industry"]}.

            TU PERFIL COMO CLIENTE POTENCIAL:
            - Objetivos de negocio: {', '.join(persona["goals"])}
            - Desafíos actuales: {', '.join(persona["challenges"])}
            - Estilo de comunicación: {guidelines["tone"]}
            - Nivel de interés inicial: {settings["interest_level"]}/100
            - Nivel de confianza inicial: {settings["trust_level"]}/100

            SITUACIÓN:
            Un vendedor va a contactarte para presentarte un producto/servicio. Tú eres el CLIENTE POTENCIAL que está evaluando soluciones para tus desafíos de negocio.

            INSTRUCCIONES PARA MENSAJE INICIAL:
            1. Saluda de manera profesional pero amigable
            2. Menciona brevemente tu rol y empresa
            3. Expresa interés inicial en conocer sobre la solución
            4. Haz una pregunta específica relacionada con tus desafíos
            5. Mantén un tono {guidelines["tone"]} pero ligeramente escéptico
            6. Máximo 50 palabras

            EJEMPLO DE RESPUESTA:
            "Hola, soy {persona["name"]}, {persona["job_title"]} en una empresa {persona["company_size"]} de {persona["industry"]}. He escuchado sobre su solución y me interesa saber cómo podría ayudarnos con [desafío específico]. ¿Podrían contarme más detalles?"

            RESPONDE SOLO COMO {persona["name"].upper()}:
            """
        else:
            prompt = f"""
            Eres {persona["name"]}, un/a {persona["job_title"]} de {persona["age"]} años que trabaja en una empresa {persona["company_size"]} en la industria {persona["industry"]}.

            TU PERFIL COMO CLIENTE POTENCIAL:
            - Objetivos de negocio: {', '.join(persona["goals"])}
            - Desafíos actuales: {', '.join(persona["challenges"])}
            - Estilo de comunicación: {guidelines["tone"]}
            - Nivel de interés actual: {settings["interest_level"]}/100
            - Nivel de confianza actual: {settings["trust_level"]}/100

            CONTEXTO DE LA CONVERSACIÓN:
            - Tipo: {settings["type"]}
            - Situación: Estás evaluando una solución que te está presentando un vendedor
            - Tu rol: CLIENTE POTENCIAL (no vendedor)

            HISTORIAL RECIENTE:
            {history_text}

            MENSAJE DEL VENDEDOR:
            Vendedor: {user_message}

            INSTRUCCIONES COMO CLIENTE POTENCIAL:
            1. Responde como {persona["name"]} evaluando la propuesta del vendedor
            2. Usa el tono: {guidelines["tone"]}
            3. Mantén la respuesta bajo {persona_context["conversation_rules"]["max_response_length"]} caracteres
            4. Haz preguntas específicas sobre cómo la solución aborda TUS desafíos
            5. Muestra objeciones realistas (precio, tiempo, implementación, ROI)
            6. Refleja tu nivel actual de interés y confianza
            7. NO vendas nada - TÚ ERES EL CLIENTE
            8. Pide ejemplos concretos, casos de éxito, demos, etc.

            RESPONDE SOLO COMO {persona["name"].upper()} (CLIENTE):
            """

        return prompt.strip()

    def _generate_mock_response(self, persona_context: Dict[str, Any], user_message: str) -> str:
        """Generate a mock response when AI is not available."""

        persona = persona_context["persona_profile"]
        settings = persona_context["conversation_settings"]

        # Simple response based on persona characteristics
        responses = [
            f"Interesante. Como {persona['job_title']}, siempre estoy buscando soluciones que me ayuden con {persona['challenges'][0] if persona['challenges'] else 'mis desafíos diarios'}. ¿Podrías contarme más específicamente cómo tu producto aborda esto?",
            f"Entiendo. En mi experiencia en {persona['industry']}, he visto muchas propuestas similares. ¿Qué hace diferente a tu solución?",
            f"Me parece relevante para mis objetivos de {persona['goals'][0] if persona['goals'] else 'crecimiento'}. ¿Tienes casos de éxito en empresas {persona['company_size']}s como la nuestra?",
            f"Necesito entender mejor el ROI. ¿Qué resultados específicos han visto otros clientes en términos de {persona['goals'][0] if persona['goals'] else 'eficiencia'}?"
        ]

        # Select response based on conversation state
        import random
        return random.choice(responses)

    async def _analyze_conversation_state(
        self,
        messages: List[Dict[str, Any]],
        persona_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze current conversation state and persona sentiment using AI."""

        if self.gemini_model:
            return await self._analyze_with_ai(messages, persona_context)
        else:
            return self._analyze_with_simple_metrics(messages, persona_context)

    async def _analyze_with_ai(
        self,
        messages: List[Dict[str, Any]],
        persona_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze conversation using Gemini AI for real emotional intelligence."""

        try:
            prompt = self._create_conversation_analysis_prompt(messages, persona_context)

            response = self.gemini_model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.3,  # Lower temperature for consistent analysis
                    top_p=0.8,
                    top_k=40,
                    max_output_tokens=1000,
                )
            )

            # Parse AI analysis
            analysis = self._parse_ai_analysis(response.text)
            return analysis

        except Exception as e:
            logger.error(f"Error in AI conversation analysis: {e}")
            return self._analyze_with_simple_metrics(messages, persona_context)

    def _create_conversation_analysis_prompt(
        self,
        messages: List[Dict[str, Any]],
        persona_context: Dict[str, Any]
    ) -> str:
        """Create prompt for AI conversation analysis."""

        persona = persona_context["persona_profile"]
        conversation_text = ""

        for msg in messages[-10:]:  # Last 10 messages for context
            sender = "Vendedor" if msg["sender"] == "user" else persona["name"]
            conversation_text += f"{sender}: {msg['message']}\n"

        prompt = f"""
        Eres un experto en análisis de conversaciones de ventas y psicología del consumidor. Analiza esta conversación entre un vendedor y {persona["name"]}, un/a {persona["job_title"]} de {persona["age"]} años.

        PERFIL DEL CLIENTE:
        - Objetivos: {', '.join(persona["goals"])}
        - Desafíos: {', '.join(persona["challenges"])}
        - Industria: {persona["industry"]}
        - Estilo de comunicación: {persona_context["response_guidelines"]["tone"]}

        CONVERSACIÓN:
        {conversation_text}

        INSTRUCCIONES:
        Analiza el estado emocional y de interés del cliente basándote en:
        1. Lenguaje utilizado (positivo/negativo/neutral)
        2. Preguntas específicas que hace
        3. Objeciones planteadas
        4. Señales de compra
        5. Nivel de engagement
        6. Tono emocional

        FORMATO DE RESPUESTA (JSON):
        {{
          "persona_state": {{
            "interest_level": número_entre_0_y_100,
            "trust_level": número_entre_0_y_100,
            "urgency_level": número_entre_0_y_100,
            "emotional_state": "positivo/neutral/negativo/escéptico/entusiasmado",
            "engagement_level": "alto/medio/bajo",
            "decision_readiness": "listo/evaluando/inicial/resistente"
          }},
          "analytics": {{
            "conversation_score": número_entre_0_y_100,
            "key_topics_discussed": ["tema1", "tema2", "tema3"],
            "objections_raised": ["objeción1", "objeción2"],
            "buying_signals": ["señal1", "señal2"],
            "emotional_indicators": ["indicador1", "indicador2"],
            "next_best_action": "acción_específica_recomendada",
            "conversation_momentum": "creciente/estable/decreciente",
            "risk_factors": ["riesgo1", "riesgo2"]
          }},
          "insights": {{
            "persona_motivation": "motivación_principal_detectada",
            "communication_style_match": "excelente/bueno/regular/pobre",
            "trust_building_progress": "descripción_del_progreso",
            "recommended_approach": "enfoque_recomendado_siguiente"
          }}
        }}

        Responde SOLO con JSON válido, sin texto adicional.
        """

        return prompt.strip()

    def _parse_ai_analysis(self, response: str) -> Dict[str, Any]:
        """Parse AI analysis response."""
        try:
            # Clean and extract JSON
            response = response.strip()
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1

            if start_idx == -1 or end_idx == 0:
                raise ValueError("No JSON found in response")

            json_str = response[start_idx:end_idx]
            analysis = json.loads(json_str)

            # Validate required fields
            return self._validate_ai_analysis(analysis)

        except Exception as e:
            logger.error(f"Error parsing AI analysis: {e}")
            return self._get_default_analysis()

    def _validate_ai_analysis(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and normalize AI analysis."""

        # Ensure all required fields with defaults
        validated = {
            "persona_state": {
                "interest_level": max(0, min(100, analysis.get("persona_state", {}).get("interest_level", 50))),
                "trust_level": max(0, min(100, analysis.get("persona_state", {}).get("trust_level", 30))),
                "urgency_level": max(0, min(100, analysis.get("persona_state", {}).get("urgency_level", 20))),
                "emotional_state": analysis.get("persona_state", {}).get("emotional_state", "neutral"),
                "engagement_level": analysis.get("persona_state", {}).get("engagement_level", "medio"),
                "decision_readiness": analysis.get("persona_state", {}).get("decision_readiness", "evaluando")
            },
            "analytics": {
                "conversation_score": max(0, min(100, analysis.get("analytics", {}).get("conversation_score", 50))),
                "key_topics_discussed": analysis.get("analytics", {}).get("key_topics_discussed", ["producto", "beneficios"]),
                "objections_raised": analysis.get("analytics", {}).get("objections_raised", []),
                "buying_signals": analysis.get("analytics", {}).get("buying_signals", []),
                "emotional_indicators": analysis.get("analytics", {}).get("emotional_indicators", []),
                "next_best_action": analysis.get("analytics", {}).get("next_best_action", "Continuar construyendo valor"),
                "conversation_momentum": analysis.get("analytics", {}).get("conversation_momentum", "estable"),
                "risk_factors": analysis.get("analytics", {}).get("risk_factors", [])
            },
            "insights": analysis.get("insights", {
                "persona_motivation": "Mejorar eficiencia operativa",
                "communication_style_match": "bueno",
                "trust_building_progress": "En progreso, necesita más evidencia",
                "recommended_approach": "Mostrar casos de éxito específicos"
            })
        }

        return validated

    def _analyze_with_simple_metrics(
        self,
        messages: List[Dict[str, Any]],
        persona_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Fallback analysis with simple metrics."""

        total_messages = len(messages)
        user_messages = [msg for msg in messages if msg["sender"] == "user"]

        # Calculate basic metrics
        interest_level = min(50 + (total_messages * 5), 90)
        trust_level = min(30 + (total_messages * 3), 80)
        urgency_level = min(20 + (total_messages * 2), 60)

        # Analyze for buying signals and objections
        recent_messages = messages[-4:] if len(messages) > 4 else messages
        message_text = ' '.join([msg.get('message', '') for msg in recent_messages]).lower()

        buying_signals = []
        if 'price' in message_text or 'cost' in message_text:
            buying_signals.append("Pregunta sobre precio")
        if 'when' in message_text or 'timeline' in message_text:
            buying_signals.append("Pregunta sobre timeline")
        if 'demo' in message_text or 'trial' in message_text:
            buying_signals.append("Interés en demo/trial")

        objections = []
        if 'expensive' in message_text or 'budget' in message_text:
            objections.append("Preocupación por precio")
        if 'time' in message_text or 'busy' in message_text:
            objections.append("Falta de tiempo")

        return {
            "persona_state": {
                "interest_level": interest_level,
                "trust_level": trust_level,
                "urgency_level": urgency_level,
                "emotional_state": "neutral",
                "engagement_level": "medio",
                "decision_readiness": "evaluando"
            },
            "analytics": {
                "conversation_score": (interest_level + trust_level + urgency_level) // 3,
                "key_topics_discussed": ["producto", "beneficios", "implementación"],
                "objections_raised": objections,
                "buying_signals": buying_signals,
                "emotional_indicators": [],
                "next_best_action": self._suggest_next_action(interest_level, trust_level, buying_signals, objections),
                "conversation_momentum": "estable",
                "risk_factors": []
            },
            "insights": {
                "persona_motivation": "Mejorar eficiencia operativa",
                "communication_style_match": "bueno",
                "trust_building_progress": "En progreso básico",
                "recommended_approach": "Continuar con enfoque consultivo"
            }
        }

    def _get_default_analysis(self) -> Dict[str, Any]:
        """Get default analysis structure."""
        return {
            "persona_state": {
                "interest_level": 50,
                "trust_level": 30,
                "urgency_level": 20,
                "emotional_state": "neutral",
                "engagement_level": "medio",
                "decision_readiness": "evaluando"
            },
            "analytics": {
                "conversation_score": 50,
                "key_topics_discussed": ["producto", "beneficios"],
                "objections_raised": [],
                "buying_signals": [],
                "emotional_indicators": [],
                "next_best_action": "Continuar construyendo valor",
                "conversation_momentum": "estable",
                "risk_factors": []
            },
            "insights": {
                "persona_motivation": "Mejorar eficiencia operativa",
                "communication_style_match": "bueno",
                "trust_building_progress": "Inicial",
                "recommended_approach": "Enfoque consultivo y educativo"
            }
        }

    def _suggest_next_action(
        self,
        interest_level: int,
        trust_level: int,
        buying_signals: List[str],
        objections: List[str]
    ) -> str:
        """Suggest next best action based on conversation analysis."""

        if buying_signals and interest_level > 70:
            return "Proponer demo o trial"
        elif objections:
            return f"Abordar objeción: {objections[0]}"
        elif trust_level < 50:
            return "Compartir casos de éxito y testimonios"
        elif interest_level < 50:
            return "Hacer preguntas de descubrimiento"
        else:
            return "Continuar construyendo valor"

    def _ensure_conversations_directory(self):
        """Ensure the conversations directory exists."""
        if not os.path.exists(self.conversations_dir):
            os.makedirs(self.conversations_dir)
            logger.info(f"Created conversations directory: {self.conversations_dir}")

    def _save_conversation(self, conversation_data: Dict[str, Any]):
        """Save conversation to file."""
        try:
            conversation_id = conversation_data.get("conversation_id")
            if not conversation_id:
                return

            filename = f"{conversation_id}.json"
            filepath = os.path.join(self.conversations_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(conversation_data, f, ensure_ascii=False, indent=2)

            logger.info(f"Conversation saved: {filepath}")
        except Exception as e:
            logger.error(f"Error saving conversation: {e}")

    def _load_conversation(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Load conversation from file."""
        try:
            filename = f"{conversation_id}.json"
            filepath = os.path.join(self.conversations_dir, filename)

            if not os.path.exists(filepath):
                return None

            with open(filepath, 'r', encoding='utf-8') as f:
                conversation_data = json.load(f)

            logger.info(f"Conversation loaded: {filepath}")
            return conversation_data
        except Exception as e:
            logger.error(f"Error loading conversation: {e}")
            return None

    def get_conversation_history(self, persona_name: str) -> List[Dict[str, Any]]:
        """Get all conversations for a specific persona."""
        try:
            conversations = []
            if not os.path.exists(self.conversations_dir):
                return conversations

            for filename in os.listdir(self.conversations_dir):
                if filename.endswith('.json'):
                    filepath = os.path.join(self.conversations_dir, filename)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            conversation_data = json.load(f)

                        if conversation_data.get("persona_name") == persona_name:
                            conversations.append({
                                "conversation_id": conversation_data.get("conversation_id"),
                                "created_at": conversation_data.get("created_at"),
                                "status": conversation_data.get("status"),
                                "message_count": len(conversation_data.get("messages", [])),
                                "last_message": conversation_data.get("messages", [])[-1] if conversation_data.get("messages") else None
                            })
                    except Exception as e:
                        logger.error(f"Error reading conversation file {filename}: {e}")

            # Sort by creation date (newest first)
            conversations.sort(key=lambda x: x.get("created_at", ""), reverse=True)
            return conversations
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []


# Create singleton instance
conversation_simulator_service = ConversationSimulatorService()

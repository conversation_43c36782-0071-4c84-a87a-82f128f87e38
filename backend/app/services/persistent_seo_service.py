"""
Persistent SEO Analysis Service
Manages long-running SEO analyses with database persistence
"""

import logging
import uuid
import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_

from app.db.models import SEOAnalysis
from app.db.session import get_db
from app.services.seo_analyzer import seo_analyzer

logger = logging.getLogger(__name__)


class PersistentSEOService:
    """Service for managing persistent SEO analyses."""
    
    def __init__(self):
        self.active_analyses: Dict[str, asyncio.Task] = {}
    
    async def start_analysis(
        self,
        url: str,
        mode: str = "page",
        user_id: Optional[str] = None,
        enable_progress: bool = True
    ) -> Dict[str, Any]:
        """Start a new persistent SEO analysis."""

        logger.info(f"Starting persistent SEO analysis - URL: {url}, Mode: {mode}, User: {user_id}")

        # Generate unique analysis ID
        analysis_id = str(uuid.uuid4())
        logger.info(f"Generated analysis ID: {analysis_id}")

        # Create database record
        db = next(get_db())
        try:
            analysis = SEOAnalysis(
                analysis_id=analysis_id,
                user_id=user_id,
                url=url,
                mode=mode,
                status="pending",
                phase="initializing",
                status_message="Iniciando análisis...",
                created_at=datetime.utcnow(),
                estimated_completion=datetime.utcnow() + timedelta(
                    minutes=3 if mode == "page" else 45
                )
            )
            
            db.add(analysis)
            db.commit()
            db.refresh(analysis)

            logger.info(f"Created persistent analysis record in database: {analysis_id}")

            # Start background analysis task
            if enable_progress:
                logger.info(f"Starting background analysis task for: {analysis_id}")
                task = asyncio.create_task(
                    self._run_analysis_with_progress(analysis_id, url, mode)
                )
                self.active_analyses[analysis_id] = task
                logger.info(f"Background task started. Active analyses: {len(self.active_analyses)}")
            
            return {
                "status": "started",
                "analysis_id": analysis_id,
                "message": "Análisis iniciado. Use el analysis_id para obtener progreso en tiempo real.",
                "progress_endpoint": f"/api/seo/progress/{analysis_id}",
                "estimated_time": "3-5 minutos" if mode == "page" else "30-60 minutos",
                "url": url,
                "mode": mode
            }
            
        except Exception as e:
            logger.error(f"Failed to start analysis: {str(e)}")
            db.rollback()
            raise
        finally:
            db.close()
    
    async def _run_analysis_with_progress(
        self,
        analysis_id: str,
        url: str,
        mode: str
    ):
        """Run the actual analysis with progress tracking."""

        # Create a new database session for this background task
        db = next(get_db())

        try:
            # Update status to in_progress
            analysis = db.query(SEOAnalysis).filter(
                SEOAnalysis.analysis_id == analysis_id
            ).first()

            if not analysis:
                logger.error(f"Analysis not found: {analysis_id}")
                return

            analysis.status = "in_progress"
            analysis.started_at = datetime.utcnow()
            analysis.phase = "discovery"
            analysis.status_message = "Descubriendo páginas del sitio web..."
            db.commit()

            logger.info(f"Started analysis {analysis_id} for URL: {url}")
            
            # Create progress callback
            def update_progress(progress_data: Dict[str, Any]):
                try:
                    # Refresh analysis from DB
                    current_analysis = db.query(SEOAnalysis).filter(
                        SEOAnalysis.analysis_id == analysis_id
                    ).first()
                    
                    if current_analysis:
                        current_analysis.current_page = progress_data.get("current_page", 0)
                        current_analysis.total_pages = progress_data.get("total_pages", 0)
                        current_analysis.phase = progress_data.get("phase", "analysis")
                        current_analysis.status_message = progress_data.get("status", "Procesando...")
                        current_analysis.progress_data = progress_data
                        
                        # Update pages info
                        if "pages_analyzed" in progress_data:
                            current_analysis.pages_analyzed = progress_data["pages_analyzed"]
                        if "total_pages_found" in progress_data:
                            current_analysis.total_pages_found = progress_data["total_pages_found"]
                        
                        db.commit()
                        logger.info(f"Updated progress for {analysis_id}: {progress_data.get('status', 'Processing...')}")
                
                except Exception as e:
                    logger.error(f"Failed to update progress for {analysis_id}: {str(e)}")
                    db.rollback()
            
            # Run the actual analysis
            if mode == "website":
                result = await seo_analyzer.analyze_website_exhaustive(
                    url, 
                    progress_callback=update_progress
                )
            else:
                result = await seo_analyzer.analyze_website(url)
            
            # Update with final results
            analysis = db.query(SEOAnalysis).filter(
                SEOAnalysis.analysis_id == analysis_id
            ).first()
            
            if analysis:
                analysis.status = "complete"
                analysis.completed_at = datetime.utcnow()
                analysis.phase = "complete"
                analysis.status_message = "Análisis completado exitosamente"
                analysis.result_data = result
                analysis.ai_enhanced = result.get("ai_enhanced", False)
                
                if analysis.started_at:
                    analysis.processing_time = (
                        analysis.completed_at - analysis.started_at
                    ).total_seconds()
                
                # Update final counts
                if "pages_analyzed" in result:
                    analysis.pages_analyzed = result["pages_analyzed"]
                if "total_pages_found" in result:
                    analysis.total_pages_found = result["total_pages_found"]
                
                db.commit()
                logger.info(f"Analysis completed successfully: {analysis_id}")
            
        except Exception as e:
            logger.error(f"Analysis failed for {analysis_id}: {str(e)}")
            
            # Update with error
            analysis = db.query(SEOAnalysis).filter(
                SEOAnalysis.analysis_id == analysis_id
            ).first()
            
            if analysis:
                analysis.status = "error"
                analysis.completed_at = datetime.utcnow()
                analysis.phase = "error"
                analysis.status_message = f"Error en el análisis: {str(e)}"
                analysis.error_data = {
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                if analysis.started_at:
                    analysis.processing_time = (
                        analysis.completed_at - analysis.started_at
                    ).total_seconds()
                
                db.commit()
        
        finally:
            # Remove from active analyses
            if analysis_id in self.active_analyses:
                del self.active_analyses[analysis_id]
            # Close the database session
            db.close()
    
    def get_analysis_progress(self, analysis_id: str) -> Optional[Dict[str, Any]]:
        """Get current progress of an analysis."""
        
        db = next(get_db())
        try:
            analysis = db.query(SEOAnalysis).filter(
                SEOAnalysis.analysis_id == analysis_id
            ).first()
            
            if not analysis:
                return None
            
            progress_data = {
                "status": analysis.status,
                "analysis_id": analysis.analysis_id,
                "url": analysis.url,
                "mode": analysis.mode,
                "phase": analysis.phase,
                "current_page": analysis.current_page,
                "total_pages": analysis.total_pages,
                "status_message": analysis.status_message,
                "created_at": analysis.created_at.isoformat() if analysis.created_at else None,
                "started_at": analysis.started_at.isoformat() if analysis.started_at else None,
                "completed_at": analysis.completed_at.isoformat() if analysis.completed_at else None,
                "estimated_completion": analysis.estimated_completion.isoformat() if analysis.estimated_completion else None,
                "processing_time": analysis.processing_time,
                "pages_analyzed": analysis.pages_analyzed,
                "total_pages_found": analysis.total_pages_found,
                "failed_urls_count": analysis.failed_urls_count,
                "ai_enhanced": analysis.ai_enhanced
            }
            
            # Add detailed progress data if available
            if analysis.progress_data:
                progress_data.update(analysis.progress_data)
            
            # Add result data if complete
            if analysis.status == "complete" and analysis.result_data:
                progress_data["result"] = analysis.result_data
            
            # Add error data if failed
            if analysis.status == "error" and analysis.error_data:
                progress_data["error"] = analysis.error_data
            
            return progress_data
            
        except Exception as e:
            logger.error(f"Failed to get progress for {analysis_id}: {str(e)}")
            return None
        finally:
            db.close()
    
    def get_user_analyses(
        self, 
        user_id: Optional[str] = None, 
        limit: int = 50,
        status_filter: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get list of analyses for a user."""
        
        db = next(get_db())
        try:
            query = db.query(SEOAnalysis)
            
            if user_id:
                query = query.filter(SEOAnalysis.user_id == user_id)
            
            if status_filter:
                query = query.filter(SEOAnalysis.status == status_filter)
            
            analyses = query.order_by(desc(SEOAnalysis.created_at)).limit(limit).all()
            
            return [analysis.to_dict() for analysis in analyses]
            
        except Exception as e:
            logger.error(f"Failed to get user analyses: {str(e)}")
            return []
        finally:
            db.close()
    
    async def cancel_analysis(self, analysis_id: str) -> bool:
        """Cancel a running analysis."""
        
        # Cancel the background task if running
        if analysis_id in self.active_analyses:
            task = self.active_analyses[analysis_id]
            task.cancel()
            del self.active_analyses[analysis_id]
        
        # Update database status
        db = next(get_db())
        try:
            analysis = db.query(SEOAnalysis).filter(
                SEOAnalysis.analysis_id == analysis_id
            ).first()
            
            if analysis and analysis.status in ["pending", "in_progress"]:
                analysis.status = "cancelled"
                analysis.completed_at = datetime.utcnow()
                analysis.status_message = "Análisis cancelado por el usuario"
                
                if analysis.started_at:
                    analysis.processing_time = (
                        analysis.completed_at - analysis.started_at
                    ).total_seconds()
                
                db.commit()
                logger.info(f"Analysis cancelled: {analysis_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to cancel analysis {analysis_id}: {str(e)}")
            db.rollback()
            return False
        finally:
            db.close()


# Global instance
persistent_seo_service = PersistentSEOService()

"""Schemas for image erase functionality using Stability AI v2beta API."""

from typing import Optional, Literal, Dict, Any
from pydantic import BaseModel, Field


class EraseRequest(BaseModel):
    """Request schema for erasing objects from images using Stability AI v2beta API."""

    # Parámetros opcionales según la documentación
    # NOTA: Erase NO requiere prompt, solo imagen y máscara opcional
    grow_mask: Optional[int] = Field(
        default=5,
        ge=0,
        le=20,
        description="Grows the edges of the mask outward in all directions by the specified number of pixels"
    )

    seed: Optional[int] = Field(
        default=0,
        ge=0,
        le=4294967294,
        description="A specific value that is used to guide the 'randomness' of the generation (0 = random)"
    )

    output_format: Optional[Literal["jpeg", "png", "webp"]] = Field(
        default="webp",
        description="Dictates the content-type of the generated image"
    )


class EraseResponse(BaseModel):
    """Response schema for erase operation."""

    image: str = Field(..., description="Base64 encoded image with objects erased")
    seed: Optional[int] = Field(None, description="Seed used for generation")
    finish_reason: Optional[str] = Field(None, description="Reason processing finished")


class FrontendEraseRequest(BaseModel):
    """Frontend request schema for erase operation."""

    grow_mask: Optional[int] = Field(default=5, ge=0, le=20)
    seed: Optional[int] = Field(default=0, ge=0, le=4294967294)
    output_format: Optional[Literal["jpeg", "png", "webp"]] = Field(default="webp")


class FrontendEraseResponse(BaseModel):
    """Frontend response schema for erase operation."""

    success: bool = Field(..., description="Whether the erase operation was successful")
    image_url: Optional[str] = Field(None, description="Data URL of the processed image")
    error: Optional[str] = Field(None, description="Error message if processing failed")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata about the operation")

"""Schemas for image-to-video generation requests and responses using Stability AI v2beta."""

from typing import Dict, Any, Optional, Literal, List
from pydantic import BaseModel, Field
from fastapi import UploadFile

class ImageToVideoRequest(BaseModel):
    """Request schema for image-to-video generation using Stability AI v2beta API."""
    
    # Required field - the image file will be handled separately in the endpoint
    # This schema is for the form data parameters only
    
    seed: Optional[int] = Field(
        default=0,
        ge=0,
        le=4294967294,
        description="A specific value that is used to guide the 'randomness' of the generation. (Omit this parameter or pass 0 to use a random seed.)"
    )
    
    cfg_scale: Optional[float] = Field(
        default=1.8,
        ge=0,
        le=10,
        description="How strongly the video sticks to the original image. Use lower values to allow the model more freedom to make changes and higher values to correct motion distortions."
    )
    
    motion_bucket_id: Optional[int] = Field(
        default=127,
        ge=1,
        le=255,
        description="Lower values generally result in less motion in the output video, while higher values generally result in more motion."
    )

class ImageToVideoStartResponse(BaseModel):
    """Response schema when starting image-to-video generation."""
    id: str = Field(..., description="Generation ID to poll for results")

class ImageToVideoResultResponse(BaseModel):
    """Response schema for completed image-to-video generation."""
    id: str = Field(..., description="Generation ID")
    status: Literal["in-progress", "completed", "failed"] = Field(..., description="Generation status")
    video: Optional[str] = Field(None, description="Base64 encoded video data (when status is completed)")
    finish_reason: Optional[Literal["SUCCESS", "CONTENT_FILTERED"]] = Field(None, description="Reason the generation finished")
    seed: Optional[int] = Field(None, description="Seed used for generation")
    error: Optional[str] = Field(None, description="Error message if generation failed")

class FrontendVideoResponse(BaseModel):
    """Response schema compatible with frontend expectations."""
    success: bool = Field(..., description="Whether the generation was successful")
    id: Optional[str] = Field(None, description="Generation ID for polling")
    status: Optional[str] = Field(None, description="Generation status")
    video_url: Optional[str] = Field(None, description="Video URL or data URL")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata about the generation")
    error: Optional[str] = Field(None, description="Error message if generation failed")

class ImageToVideoStatusRequest(BaseModel):
    """Request schema for checking image-to-video generation status."""
    generation_id: str = Field(..., description="The generation ID to check status for")

# Validation constants based on Stability AI documentation
class VideoGenerationConstants:
    """Constants for video generation validation."""
    
    # Supported image formats
    SUPPORTED_IMAGE_FORMATS = ["image/jpeg", "image/jpg", "image/png"]
    
    # Supported dimensions (from documentation)
    SUPPORTED_DIMENSIONS = [
        (1024, 576),  # 16:9 landscape
        (576, 1024),  # 9:16 portrait  
        (768, 768),   # 1:1 square
    ]
    
    # Parameter limits
    MIN_SEED = 0
    MAX_SEED = 4294967294
    MIN_CFG_SCALE = 0
    MAX_CFG_SCALE = 10
    DEFAULT_CFG_SCALE = 1.8
    MIN_MOTION_BUCKET_ID = 1
    MAX_MOTION_BUCKET_ID = 255
    DEFAULT_MOTION_BUCKET_ID = 127
    
    # API endpoints
    STABILITY_BASE_URL = "https://api.stability.ai"
    IMAGE_TO_VIDEO_ENDPOINT = "/v2beta/image-to-video"
    IMAGE_TO_VIDEO_RESULT_ENDPOINT = "/v2beta/image-to-video/result"
    
    # Polling configuration
    POLL_INTERVAL_SECONDS = 10
    MAX_POLL_ATTEMPTS = 60  # 10 minutes max
    
    # File size limits (10MB as per API docs)
    MAX_FILE_SIZE_BYTES = 10 * 1024 * 1024

def validate_image_dimensions(width: int, height: int) -> bool:
    """Validate if image dimensions are supported by Stability AI."""
    return (width, height) in VideoGenerationConstants.SUPPORTED_DIMENSIONS

def get_closest_supported_dimensions(width: int, height: int) -> tuple[int, int]:
    """Get the closest supported dimensions for an image."""
    aspect_ratio = width / height
    
    # Find the closest supported aspect ratio
    supported_dims = VideoGenerationConstants.SUPPORTED_DIMENSIONS
    closest_dims = min(supported_dims, key=lambda dims: abs(dims[0]/dims[1] - aspect_ratio))
    
    return closest_dims

def validate_image_format(content_type: str) -> bool:
    """Validate if image format is supported by Stability AI."""
    return content_type.lower() in VideoGenerationConstants.SUPPORTED_IMAGE_FORMATS

"""
Schemas for inpaint (fill areas) functionality using Stability AI v2beta API.
"""
from typing import Optional, Literal
from pydantic import BaseModel, Field


class InpaintRequest(BaseModel):
    """Request schema for inpainting (filling areas) using Stability AI v2beta API."""

    # Parámetros requeridos
    prompt: str = Field(
        ...,
        min_length=1,
        max_length=10000,
        description="Text prompt describing what to fill in the masked areas"
    )

    # Parámetros opcionales según la documentación de Stability AI
    negative_prompt: Optional[str] = Field(
        default=None,
        max_length=10000,
        description="Text prompt describing what NOT to include in the filled areas"
    )

    grow_mask: Optional[int] = Field(
        default=5,
        ge=0,
        le=100,
        description="Grows the edges of the mask outward in all directions by the specified number of pixels"
    )

    seed: Optional[int] = Field(
        default=0,
        ge=0,
        le=4294967294,
        description="A specific value that is used to guide the 'randomness' of the generation (0 = random)"
    )

    output_format: Optional[Literal["jpeg", "png", "webp"]] = Field(
        default="png",
        description="Dictates the content-type of the generated image"
    )

    style_preset: Optional[Literal[
        "3d-model", "analog-film", "anime", "cinematic", "comic-book",
        "digital-art", "enhance", "fantasy-art", "isometric", "line-art",
        "low-poly", "modeling-compound", "neon-punk", "origami",
        "photographic", "pixel-art", "tile-texture"
    ]] = Field(
        default=None,
        description="Guides the image model towards a particular style"
    )


class InpaintResponse(BaseModel):
    """Response schema from Stability AI inpaint API."""
    image: str = Field(..., description="Base64 encoded image data")
    seed: Optional[int] = Field(None, description="Seed used for generation")
    finish_reason: Optional[str] = Field(None, description="Reason for completion")


class FrontendInpaintResponse(BaseModel):
    """Response schema compatible with frontend expectations."""
    success: bool = Field(..., description="Whether the inpaint operation was successful")
    image_url: Optional[str] = Field(None, description="URL or data URL of the processed image")
    seed: Optional[int] = Field(None, description="Seed used for generation")
    finish_reason: Optional[str] = Field(None, description="Reason for completion")
    error: Optional[str] = Field(None, description="Error message if operation failed")
    metadata: dict = Field(default_factory=dict, description="Additional metadata")

"""Schemas for image generation requests and responses using Stability AI v2beta."""

from typing import Dict, Any, Optional, Literal, List
from pydantic import BaseModel, Field

class ImageGenerationRequest(BaseModel):
    """Request schema for image generation using Stability AI v2beta API."""
    prompt: str = Field(..., description="The prompt to generate the image from", max_length=10000)
    negative_prompt: Optional[str] = Field(None, description="What you do NOT want to see in the image", max_length=10000)

    # Aspect ratio instead of width/height for v2beta
    aspect_ratio: Optional[Literal["16:9", "1:1", "21:9", "2:3", "3:2", "4:5", "5:4", "9:16", "9:21"]] = Field(
        default="1:1",
        description="Aspect ratio of the generated image"
    )

    # Legacy width/height for backward compatibility
    height: int = Field(default=1024, ge=64, le=2048, description="Height of the generated image (legacy)")
    width: int = Field(default=1024, ge=64, le=2048, description="Width of the generated image (legacy)")

    # v2beta specific fields
    output_format: Optional[Literal["jpeg", "png", "webp"]] = Field(
        default="webp",
        description="Output format of the generated image"
    )

    style_preset: Optional[Literal[
        "3d-model", "analog-film", "anime", "cinematic", "comic-book",
        "digital-art", "enhance", "fantasy-art", "isometric", "line-art",
        "low-poly", "modeling-compound", "neon-punk", "origami",
        "photographic", "pixel-art", "tile-texture"
    ]] = Field(None, description="Style preset to guide the image generation")

    seed: Optional[int] = Field(
        None,
        description="Seed for reproducible generation (0-4294967294, 0 = random)",
        ge=0,
        le=4294967294
    )

    # Model selection
    model: Optional[Literal["ultra", "core", "sd3"]] = Field(
        default="ultra",
        description="Model to use: ultra (highest quality), core (fast/affordable), sd3 (latest)"
    )

    # Legacy fields for backward compatibility (not used in v2beta)
    cfg_scale: float = Field(default=7.0, ge=0, le=35, description="Legacy: CFG scale (not used in v2beta)")
    steps: int = Field(default=50, ge=10, le=150, description="Legacy: Steps (not used in v2beta)")
    samples: int = Field(default=1, ge=1, le=4, description="Legacy: Samples (not used in v2beta)")

class ImageGenerationResponse(BaseModel):
    """Response schema for image generation."""
    image: str = Field(..., description="Base64 encoded image data")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata about the generation")
    error: Optional[str] = Field(None, description="Error message if generation failed")

class BackgroundRemovalRequest(BaseModel):
    """Request schema for background removal using Stability AI v2beta API."""
    output_format: Optional[Literal["png", "webp"]] = Field(
        default="webp",
        description="Output format of the processed image"
    )

class BackgroundRemovalResponse(BaseModel):
    """Response schema for background removal."""
    image: str = Field(..., description="Base64 encoded image with background removed")
    seed: Optional[int] = Field(None, description="Seed used for generation")
    finish_reason: Optional[str] = Field(None, description="Reason processing finished")

class FrontendBackgroundRemovalResponse(BaseModel):
    """Response schema for frontend background removal."""
    success: bool = Field(..., description="Whether the background removal was successful")
    url: Optional[str] = Field(None, description="URL of the processed image")
    error: Optional[str] = Field(None, description="Error message if processing failed")
    metadata: Optional[dict] = Field(None, description="Additional metadata")

class FrontendImageResponse(BaseModel):
    """Response schema compatible with frontend expectations."""
    success: bool = Field(..., description="Whether the generation was successful")
    images: Optional[List[str]] = Field(None, description="Array of generated image URLs/data URLs")
    tipo: Optional[str] = Field(None, description="Type of response: 'sincrono' or 'asincrono'")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata about the generation")
    error: Optional[str] = Field(None, description="Error message if generation failed")

class StyleReferenceRequest(BaseModel):
    """Request schema for style reference using Stability AI v2beta API."""
    prompt: str = Field(..., description="Description of what you want to see in the output image", max_length=10000)
    negative_prompt: Optional[str] = Field(None, description="What you do NOT want to see in the output image", max_length=10000)

    # Aspect ratio
    aspect_ratio: Optional[Literal["16:9", "1:1", "21:9", "2:3", "3:2", "4:5", "5:4", "9:16", "9:21"]] = Field(
        default="1:1",
        description="Aspect ratio of the generated image"
    )

    # Style fidelity
    fidelity: Optional[float] = Field(
        default=0.5,
        description="How closely the output image's style resembles the input image's style (0.0-1.0)",
        ge=0.0,
        le=1.0
    )

    seed: Optional[int] = Field(
        default=0,
        description="Seed for reproducible generation (0-4294967294, 0 = random)",
        ge=0,
        le=4294967294
    )

    output_format: Optional[Literal["jpeg", "png", "webp"]] = Field(
        default="png",
        description="Output format of the generated image"
    )

    style_preset: Optional[Literal[
        "3d-model", "analog-film", "anime", "cinematic", "comic-book",
        "digital-art", "enhance", "fantasy-art", "isometric", "line-art",
        "low-poly", "modeling-compound", "neon-punk", "origami",
        "photographic", "pixel-art", "tile-texture"
    ]] = Field(None, description="Style preset to guide the image generation")

class StyleReferenceResponse(BaseModel):
    """Response schema for style reference."""
    image: str = Field(..., description="Base64 encoded generated image")
    seed: Optional[int] = Field(None, description="Seed used for generation")
    finish_reason: Optional[str] = Field(None, description="Reason generation finished")

class FrontendStyleReferenceResponse(BaseModel):
    """Response schema for frontend style reference."""
    success: bool = Field(..., description="Whether the style reference generation was successful")
    image_url: Optional[str] = Field(None, description="Data URL of the generated image")
    seed: Optional[int] = Field(None, description="Seed used for generation")
    finish_reason: Optional[str] = Field(None, description="Reason generation finished")
    metadata: Optional[dict] = Field(None, description="Additional metadata")
    error: Optional[str] = Field(None, description="Error message if generation failed")

class GPTImageGenerationRequest(BaseModel):
    """Request schema for GPT-4 image generation using gpt-image-1 model."""
    prompt: str = Field(..., description="The prompt to generate the image from", max_length=10000)

    # Image specifications
    size: Optional[Literal["1024x1024", "1792x1024", "1024x1792"]] = Field(
        default="1024x1024",
        description="Size of the generated image"
    )

    quality: Optional[Literal["low", "medium", "high", "auto"]] = Field(
        default="high",
        description="Quality of the generated image"
    )

    # Note: style parameter removed as it's not supported by images/edits endpoint
    # style: Optional[Literal["vivid", "natural"]] = Field(
    #     default="vivid",
    #     description="Style of the generated image"
    # )

class GPTImageGenerationResponse(BaseModel):
    """Response schema for GPT-4 image generation."""
    success: bool = Field(..., description="Whether the generation was successful")
    image_url: Optional[str] = Field(None, description="Data URL or URL of the generated image")
    revised_prompt: Optional[str] = Field(None, description="Revised prompt used by GPT-4")
    metadata: Optional[dict] = Field(None, description="Additional metadata")
    error: Optional[str] = Field(None, description="Error message if generation failed")

"""Schemas for sketch-to-image functionality."""

from pydantic import BaseModel, Field
from typing import Optional


class SketchToImageRequest(BaseModel):
    """Request schema for sketch-to-image generation."""
    prompt: str = Field(..., min_length=1, max_length=10000, description="What you wish to see in the output image")
    negative_prompt: Optional[str] = Field(None, max_length=10000, description="What you do not wish to see in the output image")
    control_strength: Optional[float] = Field(0.7, ge=0.0, le=1.0, description="How much influence the sketch has on the generation")
    seed: Optional[int] = Field(0, ge=0, le=4294967294, description="Random seed for generation")
    output_format: Optional[str] = Field("png", description="Output format (jpeg, png, webp)")
    style_preset: Optional[str] = Field(None, description="Style preset to guide the image generation")


class SketchToImageResponse(BaseModel):
    """Response schema for sketch-to-image generation."""
    image: str = Field(..., description="Base64 encoded generated image")
    seed: Optional[int] = Field(None, description="Seed used for generation")
    finish_reason: str = Field(..., description="Reason the generation finished")


class FrontendSketchResponse(BaseModel):
    """Frontend response schema for sketch-to-image."""
    success: bool = Field(..., description="Whether the generation was successful")
    image_url: Optional[str] = Field(None, description="Data URL of the generated image")
    seed: Optional[int] = Field(None, description="Seed used for generation")
    finish_reason: Optional[str] = Field(None, description="Reason the generation finished")
    error: Optional[str] = Field(None, description="Error message if generation failed")
    metadata: Optional[dict] = Field(None, description="Additional metadata")

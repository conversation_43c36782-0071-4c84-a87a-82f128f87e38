"""
Schemas for upscale (image quality enhancement) functionality using Stability AI v2beta API.
"""
from pydantic import BaseModel, Field
from typing import Optional, Literal
from enum import Enum


class UpscaleMode(str, Enum):
    """Available upscale modes"""
    FAST = "fast"
    CONSERVATIVE = "conservative" 
    CREATIVE = "creative"


class StylePreset(str, Enum):
    """Available style presets for creative upscale"""
    MODEL_3D = "3d-model"
    ANALOG_FILM = "analog-film"
    ANIME = "anime"
    CINEMATIC = "cinematic"
    COMIC_BOOK = "comic-book"
    DIGITAL_ART = "digital-art"
    ENHANCE = "enhance"
    FANTASY_ART = "fantasy-art"
    ISOMETRIC = "isometric"
    LINE_ART = "line-art"
    LOW_POLY = "low-poly"
    MODELING_COMPOUND = "modeling-compound"
    NEON_PUNK = "neon-punk"
    ORIGAMI = "origami"
    PHOTOGRAPHIC = "photographic"
    PIXEL_ART = "pixel-art"
    TILE_TEXTURE = "tile-texture"


class UpscaleRequest(BaseModel):
    """Request schema for upscale operations"""
    mode: UpscaleMode = Field(
        default=UpscaleMode.FAST,
        description="Upscale mode: fast (4x, ~1s), conservative (4K, preserves), creative (4K, reimagines)"
    )
    prompt: Optional[str] = Field(
        default=None,
        max_length=10000,
        description="Description for conservative/creative modes (required for those modes)"
    )
    negative_prompt: Optional[str] = Field(
        default=None,
        max_length=10000,
        description="What you do NOT want to see (advanced feature)"
    )
    seed: Optional[int] = Field(
        default=None,
        ge=0,
        le=4294967294,
        description="Seed for randomness (0 for random)"
    )
    output_format: Literal["jpeg", "png", "webp"] = Field(
        default="webp",
        description="Output image format"
    )
    creativity: Optional[float] = Field(
        default=0.3,
        ge=0.1,
        le=0.5,
        description="Creativity level for creative mode (0.1-0.5)"
    )
    style_preset: Optional[StylePreset] = Field(
        default=None,
        description="Style preset for creative mode"
    )

    class Config:
        use_enum_values = True


class UpscaleResponse(BaseModel):
    """Response schema for upscale operations"""
    image: str = Field(description="Base64 encoded upscaled image")
    seed: Optional[int] = Field(description="Seed used for generation")
    finish_reason: str = Field(description="Reason the generation finished")
    mode: str = Field(description="Upscale mode used")


class UpscaleInitialResponse(BaseModel):
    """Initial response for async upscale operations (creative mode)"""
    generation_id: str = Field(description="ID for polling the result")
    message: str = Field(description="Status message")


class UpscaleStatusResponse(BaseModel):
    """Status response for polling upscale operations"""
    success: bool = Field(description="Whether the operation was successful")
    status: Optional[str] = Field(description="Current status (IN_PROGRESS, COMPLETED, FAILED)")
    image_url: Optional[str] = Field(description="Data URL of the upscaled image when completed")
    seed: Optional[int] = Field(description="Seed used for generation")
    finish_reason: Optional[str] = Field(description="Reason the generation finished")
    error: Optional[str] = Field(description="Error message if failed")

"""
Schemas for 3D generation functionality using Stability AI.
"""

from pydantic import BaseModel, <PERSON>
from typing import Optional
from enum import Enum


class TextureResolution(str, Enum):
    """Texture resolution options for 3D generation."""
    LOW = "512"
    MEDIUM = "1024"
    HIGH = "2048"


class RemeshType(str, Enum):
    """Remesh algorithm options for 3D generation."""
    NONE = "none"
    TRIANGLE = "triangle"
    QUAD = "quad"


class TargetType(str, Enum):
    """Target type for Point Aware 3D generation."""
    NONE = "none"
    VERTEX = "vertex"
    FACE = "face"


class Model3DType(str, Enum):
    """3D model generation types."""
    FAST = "fast"  # Stable Fast 3D
    POINT_AWARE = "point_aware"  # Stable Point Aware 3D


class Generate3DRequest(BaseModel):
    """Request schema for 3D generation."""
    model_type: Optional[Model3DType] = Field(
        default=Model3DType.FAST,
        description="Type of 3D model generation to use"
    )
    texture_resolution: Optional[TextureResolution] = Field(
        default=TextureResolution.MEDIUM,
        description="Resolution of the textures (512, 1024, or 2048 pixels)"
    )
    foreground_ratio: Optional[float] = Field(
        default=0.85,
        description="Controls padding around the object"
    )
    remesh: Optional[RemeshType] = Field(
        default=RemeshType.NONE,
        description="Remeshing algorithm to use"
    )
    vertex_count: Optional[int] = Field(
        default=-1,
        ge=-1,
        le=20000,
        description="Target vertex count (-1 for no limit, Fast 3D only)"
    )
    # Point Aware 3D specific parameters
    target_type: Optional[TargetType] = Field(
        default=TargetType.NONE,
        description="Target type for mesh simplification (Point Aware 3D only)"
    )
    target_count: Optional[int] = Field(
        default=1000,
        ge=100,
        le=20000,
        description="Target vertex/face count (Point Aware 3D only)"
    )
    guidance_scale: Optional[float] = Field(
        default=3.0,
        ge=1.0,
        le=10.0,
        description="Guidance scale for point diffusion (Point Aware 3D only)"
    )
    seed: Optional[int] = Field(
        default=0,
        ge=0,
        le=4294967294,
        description="Seed for randomness (Point Aware 3D only)"
    )


class Generate3DResponse(BaseModel):
    """Response schema for 3D generation."""
    success: bool
    model_data: Optional[str] = Field(None, description="Base64 encoded GLB file")
    filename: Optional[str] = Field(None, description="Suggested filename for download")
    size_bytes: Optional[int] = Field(None, description="Size of the GLB file in bytes")
    metadata: Optional[dict] = Field(None, description="Additional metadata")
    error: Optional[str] = Field(None, description="Error message if generation failed")


class FrontendGenerate3DResponse(BaseModel):
    """Frontend response schema for 3D generation."""
    success: bool
    model_url: Optional[str] = Field(None, description="Data URL for the GLB file")
    filename: Optional[str] = Field(None, description="Suggested filename")
    size_mb: Optional[float] = Field(None, description="Size in megabytes")
    metadata: Optional[dict] = Field(None, description="Generation metadata")
    error: Optional[str] = Field(None, description="Error message")

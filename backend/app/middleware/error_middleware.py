"""Custom middleware for error handling and logging in the FastAPI application."""

import logging
import socket
import time
import traceback
from typing import Any, Dict, Optional, Union, Callable, Awaitable, cast

from starlette.requests import Request
from starlette.exceptions import HTTPException
from starlette.responses import <PERSON><PERSON><PERSON><PERSON>ponse, Response
from starlette.middleware.base import BaseHT<PERSON>Middleware, RequestResponseEndpoint
from starlette.status import HTTP_500_INTERNAL_SERVER_ERROR
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class ErrorResponse(BaseModel):
    error: str
    message: str
    status_code: int
    request_id: str

class ExceptionHandlerMiddleware(BaseHTTPMiddleware):
    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        request_id = getattr(request.state, "request_id", "N/A")
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            request_id = getattr(request.state, "request_id", "unknown")
            error_message = f"Internal Server Error: {e}"
            status_code = 500  # Default status code
            detail = "An unexpected internal server error occurred."

            # Check if it's an HTTPException *after* catching the generic Exception
            if isinstance(e, HTTPException):
                # Use status_code and detail from the HTTPException
                status_code = e.status_code
                detail = e.detail
                error_message = f"HTTP Error {status_code}: {detail} - Request ID: {request_id}"
                logger.warning(error_message, exc_info=False) # Log HTTP exceptions as warnings
            else:
                 # Log other exceptions as errors with traceback
                error_message = f"Internal Server Error - Request ID: {request_id}: {e}"
                logger.error(error_message, exc_info=True)

            # Construct the JSON response
            response_content = ErrorResponse(
                error="Internal Server Error",
                message=detail, # Use the detail (generic or specific)
                status_code=status_code,
                request_id=request_id
            ).dict()

            return JSONResponse(
                status_code=status_code,
                content=response_content,
            )

class StructuredErrorMiddleware(BaseHTTPMiddleware):
    """Middleware to catch exceptions and return structured JSON error responses."""

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        try:
            response = await call_next(request)
            return response
        except Exception as e: 
            request_id = getattr(request.state, "request_id", "N/A")
            timestamp = time.strftime("%Y-%m-%dT%H:%M:%S%z")
            tb_str = traceback.format_exc()
            error_code = "internal_server_error"
            status_code = HTTP_500_INTERNAL_SERVER_ERROR
            message = "An unexpected internal server error occurred."

            if isinstance(e, HTTPException):
                status_code = e.status_code
                detail = e.detail
                
                # Process the detail value based on its type
                # Explicitly handle detail as Any to avoid typing issues
                message = ""
                error_code = f"http_error_{status_code}"
                
                if hasattr(detail, "get") and callable(detail.get):
                    # Treat as dict-like object
                    message = detail.get("message", str(detail))
                    error_code = detail.get("code", f"http_error_{status_code}")
                else:
                    # Treat as string or other type
                    message = str(detail)

                logger.warning(
                    "HTTPException caught: Status=%s Code=%s Msg=%s RequestID=%s",
                    status_code, error_code, message, request_id
                )
            else:
                logger.error(
                    "Unhandled exception caught: Type=%s Error=%s RequestID=%s\n%s",
                    type(e).__name__, e, request_id, tb_str,
                    exc_info=True
                )

            content = {
                "status": "error",
                "error": {
                    "code": error_code,
                    "message": message,
                    "request_id": request_id,
                    "timestamp": timestamp,
                },
            }
            return JSONResponse(status_code=status_code, content=content)

# Correct signature that matches how FastAPI middleware works
async def log_request_middleware(request: Request, call_next: Callable) -> Response:
    """Logs request/response details and processing time."""
    start_time = time.time()
    request_id = request.state.request_id
    try:
        # Make sure we properly type the response
        response: Response = await call_next(request)
        process_time = (time.time() - start_time) * 1000
        status_code = response.status_code
        logger.info(
            "Request completed: Method=%s Path=%s Status=%d Duration=%.2fms RequestID=%s",
            request.method, request.url.path, status_code, process_time, request_id
        )
    except Exception as e:
        process_time = (time.time() - start_time) * 1000
        logger.error(
            "Request failed: Method=%s Path=%s Duration=%.2fms Error=%s RequestID=%s", 
            request.method, request.url.path, process_time, e, request_id, exc_info=True
        )
        raise e
    
    # Return without unnecessary cast
    return response

class NetworkDebugMiddleware(BaseHTTPMiddleware):
    """Logs network-related debug information like client IP and server host."""
    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        client_host = request.client.host if request.client else "Unknown"
        logger.debug("Incoming request from: %s", client_host) 
        try:
            server_name = socket.gethostname()
            server_ip = socket.gethostbyname(server_name)
            logger.debug("Request received by server: Hostname=%s IP=%s", server_name, server_ip) 
        except socket.gaierror as e:
            logger.warning("Could not resolve server hostname/IP: %s", e)

        # Ensure we're returning a Response type
        response: Response = await call_next(request)

        logger.debug("Sending response with status: %s", response.status_code)

        return response

class TracebackMiddleware(BaseHTTPMiddleware):
    """Outermost middleware to catch and log any critical unhandled exceptions."""
    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        try:
            return await call_next(request)
        except Exception as e:
            tb_str = traceback.format_exc()
            request_id = getattr(request.state, "request_id", "N/A")
            logger.critical(
                "Critical unhandled exception at boundary: Type=%s Error=%s RequestID=%s\n%s",
                type(e).__name__, e, request_id, tb_str,
                exc_info=True
            )

            content = {
                "status": "error",
                "error": {
                    "code": "critical_internal_server_error",
                    "message": "A critical internal server error occurred. Please contact support.",
                    "request_id": request_id
                }
            }
            return JSONResponse(status_code=HTTP_500_INTERNAL_SERVER_ERROR, content=content)

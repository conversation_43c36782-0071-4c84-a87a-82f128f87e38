"""
Reusable dependencies for FastAPI endpoints.

This module provides common dependencies that can be used across
different API endpoints for consistent behavior.
"""

import logging
from typing import Optional, Dict, Any, List, Generator

from fastapi import Depends, HTTPException, Header, Request
from fastapi.security import <PERSON><PERSON><PERSON>Header
from sqlalchemy.orm import Session

from app.core.config import settings
from app.db.session import get_db as _get_db

logger = logging.getLogger(__name__)

# API Key security scheme
api_key_header = APIKeyHeader(name=settings.API_KEY_NAME, auto_error=False)


async def verify_api_key(
    api_key: Optional[str] = Depends(api_key_header),
    request: Request = None
) -> Optional[str]:
    """
    Verify that a valid API key is provided in the request header.

    Args:
        api_key: The API key from the request header
        request: The FastAPI request object

    Returns:
        The API key if valid

    Raises:
        HTTPException: If the API key is invalid or missing
    """
    # Skip API key verification in development mode if no keys are configured
    if settings.ENVIRONMENT == "development" and not settings.API_KEY:
        logger.warning("API key verification skipped in development mode")
        return None

    # Check if API key is provided
    if not api_key:
        logger.warning("Missing API key in request")
        raise HTTPException(
            status_code=401,
            detail={
                "code": "missing_api_key",
                "message": "API key is required"
            }
        )

    # Check if API key is valid
    valid_keys = settings.API_KEY.split(",") if settings.API_KEY else []
    if api_key not in valid_keys:
        logger.warning(f"Invalid API key: {api_key[:5]}...")
        raise HTTPException(
            status_code=401,
            detail={
                "code": "invalid_api_key",
                "message": "Invalid API key"
            }
        )

    logger.debug("API key verified successfully")
    return api_key


async def get_external_api_key(service_name: str) -> str:
    """
    Get an API key for an external service from the backend.

    Args:
        service_name: The name of the external service (e.g., 'gemini', 'stability')

    Returns:
        The API key for the specified service

    Raises:
        HTTPException: If the API key is not configured or invalid
    """
    service_name = service_name.lower()

    # Map service names to environment variables
    service_map = {
        "gemini": settings.GEMINI_API_KEY,
        "stability": settings.STABILITY_API_KEY,
        "elevenlabs": settings.ELEVENLABS_API_KEY,
    }

    # Get the API key for the requested service
    api_key = service_map.get(service_name)

    # Check if API key is configured
    if not api_key:
        logger.error(f"API key for {service_name} is not configured")
        raise HTTPException(
            status_code=500,
            detail={
                "code": "api_key_not_configured",
                "message": f"API key for {service_name} is not configured"
            }
        )

    return api_key


# Re-export the get_db function from app.db.session
def get_db() -> Generator[Session, None, None]:
    """
    Get a database session.

    This function is a re-export of the get_db function from app.db.session.
    It's provided here for convenience and backward compatibility.

    Returns:
        Generator[Session, None, None]: A SQLAlchemy session
    """
    return _get_db()

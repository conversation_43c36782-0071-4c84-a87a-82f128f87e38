# Poetry Migration Fixes

This document details the issues encountered during the migration to Poetry and the solutions implemented to address them.

## Issue 1: Missing Dependency Groups

### Problem
The initial migration to Poetry failed to properly organize dependencies into logical groups. While the `dev` group was created, the following essential groups were missing:
- `web` (FastAPI, Uvicorn, etc.)
- `database` (SQLAlchemy, Alembic, etc.)
- `ai` (OpenAI, LangChain, etc.)
- `security` (Python-Jose, Passlib, etc.)

This caused Poetry to place all packages in the main dependency section, making it difficult to selectively install dependencies based on environment needs.

### Solution
We've updated `pyproject.toml` to include these missing groups and organized dependencies accordingly:

```toml
[tool.poetry.group.web.dependencies]
fastapi = "^0.115.0"
uvicorn = {extras = ["standard"], version = "^0.34.0"}
pydantic = "^2.0.0"
pydantic-settings = "^2.0.0"
# ...additional web dependencies

[tool.poetry.group.database.dependencies]
sqlalchemy = "^2.0.0"
alembic = "^1.15.0"
psycopg2-binary = "^2.9.0"
# ...additional database dependencies

[tool.poetry.group.ai.dependencies]
openai = "^1.0.0"
google-generativeai = "^0.3.0"
# ...additional AI dependencies

[tool.poetry.group.security.dependencies]
python-jose = "^3.3.0"
passlib = "^1.7.4"
# ...additional security dependencies
```

This organization allows for selective installation based on environment needs:
```bash
# Production environment (no dev dependencies)
poetry install --without dev

# Minimal install (only web and database)
poetry install --only main,web,database

# Full install including all groups
poetry install
```

## Issue 2: Version Conflicts with grpcio

### Problem
Poetry was attempting to downgrade `grpcio` from version 1.71.0 (in requirements.txt) to 1.63.2, indicating potential dependency version conflicts.

Logs showed:
```
grpcio (1.71.0 -> 1.63.2) (because langchain-community<0.X.Y requires grpcio<1.64.0)
```

This downgrade could potentially break compatibility with other packages that require newer grpcio versions.

### Solution
We've updated the dependency constraints to resolve this conflict by:

1. Specifying a compatible version range for grpcio:
   ```toml
   grpcio = "^1.71.0"
   ```

2. Updating LangChain related packages to versions that support newer grpcio:
   ```toml
   langchain = "^0.1.0"
   langchain-core = "^0.1.0"
   langchain-community = "^0.1.0"
   langchain-openai = "^0.1.0"
   ```

This ensures we maintain compatibility while using the most recent stable versions.

## Issue 3: Wildcard Version Constraints

### Problem
Many dependencies in `pyproject.toml` were using wildcard version constraints (`*`), which creates several issues:

1. Poetry cannot effectively resolve dependencies when wildcards are used
2. It may lead to unpredictable version selection
3. It undermines the reproducibility benefits of using Poetry

For example:
```toml
langchain = "*"
openai = "*"
google-generativeai = "*"
```

### Solution
We've replaced all wildcard constraints with specific version ranges using the caret syntax, which allows compatible updates while preventing breaking changes:

```toml
langchain = "^0.1.0"
langchain-core = "^0.1.0"
langchain-community = "^0.1.0"
langchain-openai = "^0.1.0"
openai = "^1.0.0"
google-generativeai = "^0.3.0"
```

This approach:
- Locks the major version (prevents breaking changes)
- Allows minor and patch updates (for bug fixes and improvements)
- Improves dependency resolution reliability

## Issue 4: Tiktoken Compatibility Issues

### Problem
The `tiktoken` package presents several challenges:
- Requires Rust compiler for installation
- Has compatibility issues with Python 3.13
- Creates complexity in Docker builds
- Dependency issues with PyO3

### Solution
We've completely removed `tiktoken` and provided alternatives as detailed in the dedicated document `TIKTOKEN_REMOVAL.md`.

## Implementation

The fixes are implemented through the `fix_poetry_migration.sh` script, which:

1. Checks and adds missing dependency groups
2. Fixes the grpcio version constraint
3. Replaces wildcard version constraints with specific ranges
4. Removes tiktoken dependency
5. Cleans Poetry cache and updates dependencies
6. Performs installation with verbose output to identify any remaining issues

To apply these fixes, run:

```bash
cd backend
chmod +x fix_poetry_migration.sh
./fix_poetry_migration.sh
```

## Additional Docker Fixes

For Docker environments, we've made the following changes:

1. Updated Dockerfile.poetry to handle dependencies properly:
   - Installs Poetry in a separate step
   - Copies only pyproject.toml and poetry.lock first (for better caching)
   - Installs dependencies with appropriate groups based on environment

2. Modified docker-compose files to use the Poetry-enabled images

See `DOCKER_POETRY_INTEGRATION.md` for more detailed Docker-related changes.

## Next Steps After Fixing

After running the fix script:

1. Test the application with Poetry:
   ```bash
   poetry run uvicorn app.main:app --reload
   ```

2. Complete the migration process:
   ```bash
   ./complete_poetry_migration.py
   ```

3. Update CI/CD pipelines to use Poetry (see `POETRY_CICD_INTEGRATION.md`)
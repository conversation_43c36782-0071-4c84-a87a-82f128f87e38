# Plan Maestro de la App: Arquitectura, Flujos y Documentación

## 1. Arquitectura General
- **Framework principal:** FastAPI (Python)
- **Orquestación de agentes:** CrewAI
- **LLM y embeddings:** Gemini (Google Generative AI)
- **Generación de imágenes:** Stability AI (Stable Diffusion), Gemini (opcional), Diffusers
- **Generación de voz/video:** ElevenLabs (voz), MoviePy, ffmpeg-python, PyAV (video)
- **Memoria/contexto:** CrewAI Knowledge + Faiss/Weaviate opcional
- **Frontend:** React/Next.js (no detallado aquí)

## 2. Flujos Principales
1. **Frontend envía prompt** → `/api/v1/crew/run` (o endpoints especializados)
2. **Backend valida y ejecuta:**
    - Valida variables de entorno (GEMINI_API_KEY, etc.)
    - Inicializa agentes y Crew
    - Ejecuta workflow de agentes
    - Maneja y reporta errores estructurados
3. **Backend responde** con resultado y reasoning trace o error estructurado
4. **Frontend muestra resultado o error al usuario**

## 3. Agentes y Herramientas
- **Emma (Orquestadora):** Coordina todo el proceso, decide qué agentes activar y cómo ensamblar el resultado final.
- **Meme Agent:** Genera imágenes tipo meme (Stable Diffusion, Gemini Image).
- **Photographic Agent:** Imágenes realistas/fotográficas (Stable Diffusion, Gemini).
- **Cinematic Agent:** Imágenes/video cinematográficos (Stable Diffusion, Diffusers, MoviePy).
- **Copy Agent:** Copywriting viral (Gemini)
- **Herramientas:**
    - `agent_generate_meme_image`
    - `agent_generate_photographic_image`
    - `agent_generate_realistic_image`
    - `generate_copy`
    - Integraciones con ElevenLabs, Stability

## 4. Endpoints Principales
- `POST /api/v1/crew/run` — Ejecuta el equipo de agentes y orquesta la campaña. 
    - **Seguridad:** Requiere header `X-API-Key` válido (configurable por variable de entorno `VALID_API_KEYS`).
    - **Validación avanzada:** El prompt es obligatorio (5-512 caracteres). Respuestas de error estructuradas.
    - **Asincronía:** El endpoint es `async` y soporta ejecución síncrona/asíncrona de agentes.
    - **Reasoning trace:** Incluye todos los pasos relevantes y errores para auditoría.
    - **Preparado para background jobs:** Fácil de migrar a ejecución en segundo plano y polling.
    - **Ejemplo de request:**
      ```json
      {
        "prompt": "Genera una campaña viral para producto X"
      }
      ```
    - **Ejemplo de respuesta (éxito):**
      ```json
      {
        "result": "...output generado...",
        "reasoning_trace": [
          {"type": "prompt", "content": "Genera una campaña viral...", "timestamp": "..."},
          {"type": "info", "content": "CrewAI team construido", "timestamp": "..."},
          {"type": "asset", "agent": "Emma", "content": "...", "timestamp": "..."},
          {"type": "info", "content": "Tiempo total de ejecución: ... ms", "timestamp": "..."}
        ]
      }
      ```
    - **Ejemplo de respuesta (error):**
      ```json
      {
        "status": "error",
        "error": {
          "code": "unauthorized",
          "message": "API Key inválida o ausente. Solicite acceso al administrador."
        },
        "reasoning_trace": [ ... ]
      }
      ```
- `POST /api/v1/content/generate` — Genera contenido (copy, imagen, etc.)
- `POST /api/v1/content/improve` — Mejora un prompt
- `POST /api/v1/content/image` — Genera imágenes con agente específico
- `GET /api/v1/content/history` — Historial de campañas/contenido
- `GET /api/v1/prompt/optimization-history` — Historial de optimización de prompts
- `GET /health` — Healthcheck

## 5. Integraciones y Variables de Entorno
- `GEMINI_API_KEY` — Obligatorio para Gemini
- `STABILITY_API_KEY` — Obligatorio para Stable Diffusion
- `ELEVENLABS_API_KEY` — Obligatorio para voz
- (Opcionales: SERPER_API_KEY, OPENAI_API_KEY, otros)

## 6. Manejo de Errores y Logs
- Middleware captura excepciones y responde con JSON estructurado:
  ```json
  {
    "error": {
      "code": "MISSING_API_KEY",
      "message": "Falta la clave ... en el entorno",
      "hint": "Agrega la clave en tu .env y reinicia el backend"
    }
  }
  ```
- Logs en archivo y consola para debugging y monitoreo.

## 7. Pruebas y Validaciones
- Pruebas unitarias para agentes y lógica core
- Pruebas de integración para endpoints

## 8. Contratos de API (ejemplo)
**Request:**
```json
{
  "prompt": "Genera una campaña viral para producto X"
}
```
**Response (éxito):**
```json
{
  "result": "...output generado...",
  "reasoning_trace": [...]
}
```
**Response (error):**
```json
{
  "error": {
    "code": "AGENT_ERROR",
    "message": "Error interno en el agente Emma",
    "hint": "Revisa el prompt o contacta soporte."
  }
}
```

## 9. Mantenimiento y Mejora Continua
- Revisa periódicamente las mejores prácticas internacionales de seguridad, observabilidad y escalabilidad para FastAPI y microservicios IA.
- Implementa rate limiting, JWT/OAuth2, CORS restrictivo, y monitoreo avanzado (Sentry, Prometheus, etc) para producción.
- Documenta y automatiza pruebas para todos los endpoints críticos.
- Este documento debe actualizarse cada vez que se agregue o modifique una integración, agente, endpoint o flujo relevante.
- Si un desarrollador agrega una nueva feature (ej: video, voz, nuevo agente), debe documentar el flujo y dependencias aquí.
- El objetivo es que cualquier desarrollador pueda entender y extender la app en minutos.

---

_Este archivo es el plan maestro vivo de la arquitectura y debe ser la referencia central para el equipo._

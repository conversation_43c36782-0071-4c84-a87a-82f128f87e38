"""
Mock Server for Testing Frontend Integration

This script creates a simple FastAPI server that mocks the behavior of the
agent system API endpoints for testing the frontend integration.
"""

import uvicorn
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import uuid
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Emma Studio Mock API",
    description="Mock API for testing frontend integration",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Define models
class ReasoningTraceItem(BaseModel):
    type: str
    content: str
    agent: Optional[str] = None
    timestamp: str
    trace_snippet: Optional[List[str]] = None

class CrewRunRequest(BaseModel):
    crew_id: str
    prompt: str
    inputs: Optional[Dict[str, Any]] = None
    context: Optional[Dict[str, Any]] = None
    config: Optional[Dict[str, Any]] = None

class CrewRunResponse(BaseModel):
    request_id: str
    status: str
    result: str
    reasoning_trace: List[ReasoningTraceItem]
    metadata: Dict[str, Any]
    error: Optional[str] = None

class AgentChatRequest(BaseModel):
    agent_id: str
    message: str
    context: Optional[Dict[str, Any]] = None

class AgentChatResponse(BaseModel):
    response: str
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

# Define routes
@app.post("/api/v1/crew/run", response_model=CrewRunResponse)
async def run_crew(request: CrewRunRequest):
    """Mock endpoint for running a crew of agents."""
    logger.info(f"Received crew run request: {request.prompt}")
    
    # Generate a unique request ID
    request_id = str(uuid.uuid4())
    
    # Create a mock response
    current_time = int(time.time() * 1000)
    
    # Create reasoning trace
    reasoning_trace = [
        ReasoningTraceItem(
            type="prompt",
            content=request.prompt,
            timestamp=str(current_time)
        ),
        ReasoningTraceItem(
            type="info",
            content="Analyzing request...",
            agent="emma",
            timestamp=str(current_time + 1000)
        ),
        ReasoningTraceItem(
            type="asset",
            content="Generated content based on the prompt.",
            agent="content",
            timestamp=str(current_time + 2000)
        )
    ]
    
    # Create response
    response = CrewRunResponse(
        request_id=request_id,
        status="success",
        result=f"This is a mock response for the prompt: '{request.prompt}'",
        reasoning_trace=reasoning_trace,
        metadata={
            "execution_time_seconds": 0.5,
            "crew_id": request.crew_id
        }
    )
    
    return response

@app.post("/api/v1/crew/chat", response_model=AgentChatResponse)
async def chat_with_agent(request: AgentChatRequest):
    """Mock endpoint for chatting with a specific agent."""
    logger.info(f"Received agent chat request: {request.message}")
    
    # Create a mock response based on the agent
    if request.agent_id == "emma":
        response = "Hello! I'm Emma, the main coordinator agent. How can I help you today?"
    elif request.agent_id == "seo":
        response = "Hi there! I'm the SEO specialist. I can help you optimize your content for search engines."
    elif request.agent_id == "content":
        response = "Greetings! I'm the Content Creator agent. I specialize in generating high-quality content."
    else:
        response = f"Hello from agent {request.agent_id}. I received your message: '{request.message}'"
    
    return AgentChatResponse(
        response=response,
        metadata={
            "agent_id": request.agent_id,
            "timestamp": time.time()
        }
    )

# Health check endpoint
@app.get("/api/v1/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok"}

# Run the server
if __name__ == "__main__":
    logger.info("Starting mock server on http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)

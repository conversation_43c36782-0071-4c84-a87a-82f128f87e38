"""
Simple Agent Test
A minimal script to test the agent system
"""

import asyncio
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the agent system
from agents import (
    BaseAgent,
    AgentContext,
    ContextType,
    AgentTask
)
from agents.specialized import EmmaAgent


class SimpleLLMProvider:
    """Simple LLM provider for demonstration purposes."""
    
    async def generate(self, prompt: str, **kwargs) -> str:
        """Generate text from a prompt."""
        logger.info(f"Generating text for prompt: {prompt[:50]}...")
        
        # In a real implementation, this would call an actual LLM
        return f"This is a simulated response to: {prompt[:50]}..."


async def run_simple_test():
    """Run a simple test of the agent system."""
    logger.info("Starting simple agent test")
    
    # Create LLM provider
    llm_provider = SimpleLLMProvider()
    
    # Create Emma agent
    emma = EmmaAgent("emma", "Emma", llm_provider)
    
    # Create a task
    task = AgentTask(
        id="task-1",
        description="Create a marketing campaign for a new smartphone",
        priority=5,
        status="pending"
    )
    
    # Create context
    context = AgentContext(
        context_type=ContextType.TASK,
        data={"task": task}
    )
    
    # Get next action
    logger.info("Getting next action")
    action = await emma.get_next_action(context)
    logger.info(f"Next action: {action.type}")
    
    # Execute action
    logger.info("Executing action")
    result = await emma.execute_action(action)
    logger.info(f"Action result: {result.success}")
    
    if result.result:
        logger.info(f"Result: {result.result}")
    if result.error:
        logger.error(f"Error: {result.error}")
    
    # Try to process the same task again (should be skipped)
    logger.info("Trying to process the same task again")
    action2 = await emma.get_next_action(context)
    logger.info(f"Next action: {action2.type}")
    
    # Execute action
    logger.info("Executing action")
    result2 = await emma.execute_action(action2)
    logger.info(f"Action result: {result2.success}")
    
    if result2.result:
        logger.info(f"Result: {result2.result}")
    if result2.error:
        logger.error(f"Error: {result2.error}")


if __name__ == "__main__":
    # Run the test
    asyncio.run(run_simple_test())

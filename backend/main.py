"""
Emma Studio Backend - Main Entry Point

This file serves as a simple wrapper around the main FastAPI application
defined in app/main.py. It ensures proper path setup and provides a
convenient entry point for running the application.

Usage:
    python -m backend.main
    # or
    uvicorn backend.main:app --reload
"""

import logging
import os
import sys

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Ensure proper path setup
BACKEND_DIR = os.path.dirname(os.path.abspath(__file__))
if BACKEND_DIR not in sys.path:
    sys.path.insert(0, BACKEND_DIR)

# Add vendorized CrewAI to path if it exists
CREWAI_PATH = os.path.join(BACKEND_DIR, "crewAI", "src")
if os.path.exists(CREWAI_PATH):
    sys.path.append(CREWAI_PATH)
    logger.info(f"Added vendorized CrewAI to path: {CREWAI_PATH}")

# Import the main FastAPI application and settings
from app.main import app
from app.core.config import settings

# Entry point for running the application directly
if __name__ == "__main__":
    import uvicorn
    logger.info(f"Starting Emma Studio backend on {settings.HOST}:{settings.PORT}...")
    uvicorn.run("app.main:app", host=settings.HOST, port=settings.PORT, reload=True)

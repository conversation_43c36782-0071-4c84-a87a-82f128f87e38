# Docker Integration with Poetry

This document explains how we've integrated Poetry with Docker for dependency management in our containerized environments. The changes ensure consistent dependency resolution across development, testing, and production environments.

## Modified Dockerfiles

We've created Poetry-specific Dockerfiles:

- `Dockerfile.poetry`: Production-ready image with Poetry
- `Dockerfile.poetry.dev`: Development image with additional tools

### Key Changes in Dockerfiles

#### 1. Base Dockerfile.poetry

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install Poetry
RUN pip install poetry==1.7.1

# Copy only dependency definition files first for better caching
COPY pyproject.toml poetry.lock* ./

# Configure Poetry to not use virtual environments in Docker
RUN poetry config virtualenvs.create false \
    && poetry install --no-dev --no-interaction --no-ansi

# Copy application code
COPY . .

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 2. Development Dockerfile.poetry.dev

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install Poetry and development tools
RUN pip install poetry==1.7.1 \
    && apt-get update \
    && apt-get install -y --no-install-recommends git curl

# Copy only dependency definition files
COPY pyproject.toml poetry.lock* ./

# Configure Poetry to not use virtual environments and install all dependencies
RUN poetry config virtualenvs.create false \
    && poetry install --no-interaction --no-ansi

# Copy application code
COPY . .

# Run the development server with hot reload
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

## Docker Compose Integration

### 1. Production Docker Compose (docker-compose.poetry.yml)

```yaml
version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.poetry
    ports:
      - "8000:8000"
    env_file:
      - .env.production
    # Additional production configuration
```

### 2. Development Override (docker-compose.poetry.override.yml)

```yaml
version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.poetry.dev
    volumes:
      - ./backend:/app
    env_file:
      - .env.development
    # Additional development configuration
```

## Build Optimizations

### Multi-stage Builds for Smaller Images

For production, we can further optimize using multi-stage builds:

```dockerfile
# Build stage
FROM python:3.11-slim as builder

WORKDIR /app

RUN pip install poetry==1.7.1

COPY pyproject.toml poetry.lock* ./
RUN poetry export -f requirements.txt > requirements.txt

# Final stage
FROM python:3.11-slim

WORKDIR /app

COPY --from=builder /app/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Layer Caching Strategy

1. Copy and install dependencies first
2. Copy application code later
3. This ensures dependencies are cached between builds when only code changes

## Selective Dependency Installation

Poetry's group feature allows selective installation of dependencies in different environments:

```dockerfile
# For minimal production image
RUN poetry install --only main,web,database --no-interaction --no-ansi

# For images requiring AI capabilities
RUN poetry install --only main,web,database,ai --no-interaction --no-ansi

# For development images with all tools
RUN poetry install --no-interaction --no-ansi
```

## CI/CD Integration

For CI/CD pipelines, we recommend:

1. Using BuildKit for faster builds:
   ```yaml
   export DOCKER_BUILDKIT=1
   docker build -f Dockerfile.poetry .
   ```

2. Caching dependencies between pipeline runs:
   ```yaml
   docker build \
     --cache-from myapp:builder-cache \
     --target builder \
     --tag myapp:builder-cache .
   ```

3. Using Poetry directly in CI for testing before building Docker images:
   ```yaml
   - name: Install dependencies
     run: poetry install
   - name: Run tests
     run: poetry run pytest
   ```

## Development Workflow with Docker and Poetry

1. **Local Development**:
   - Use Poetry locally for dependency management: `poetry add package-name`
   - Run the application with Poetry: `poetry run uvicorn app.main:app --reload`

2. **Docker Development**:
   - Use docker-compose with override file: `docker-compose -f docker-compose.poetry.yml -f docker-compose.poetry.override.yml up`
   - Container has hot-reload enabled via mounted volumes

3. **Adding New Dependencies**:
   - Add locally with Poetry: `poetry add package-name`
   - Either rebuild the container or install inside it:
     ```bash
     docker-compose exec backend poetry add package-name
     ```

## Troubleshooting Common Issues

### 1. Permission Issues

If you encounter permission issues when mounting volumes:

```yaml
volumes:
  - ./backend:/app:cached,delegated
```

### 2. Poetry Lock Conflicts

When different developers update poetry.lock:

```bash
# Resolve conflicts and update
poetry lock --no-update
```

### 3. Performance Issues

For better performance on macOS/Windows:

```yaml
volumes:
  - ./backend:/app:cached
```

## Deployment Considerations

1. **Image Size Optimization**:
   - Use multi-stage builds
   - Consider alpine-based images for smaller footprint
   - Use `--no-dev` in production builds

2. **Security**:
   - Run containers as non-root user
   - Scan images with Trivy or similar tools
   - Use minimal base images

3. **Environment Variables**:
   - Use `.env` files for local development
   - Use secrets management for production

## Migration Steps

1. Update your existing Docker workflow:
   ```bash
   # Build with the Poetry Dockerfile
   docker build -f backend/Dockerfile.poetry -t backend-poetry .
   
   # Run the container
   docker run -p 8000:8000 backend-poetry
   ```

2. Update docker-compose commands:
   ```bash
   # Use the Poetry compose files
   docker-compose -f docker-compose.poetry.yml up
   ```

3. For development with hot reload:
   ```bash
   docker-compose -f docker-compose.poetry.yml -f docker-compose.poetry.override.yml up
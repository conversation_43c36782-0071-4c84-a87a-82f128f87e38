# Emma Studio Agent System Examples

This directory contains examples of how to use the custom agent system in Emma Studio.

## Available Examples

### Custom Agent Example

The `custom_agent_example.py` file demonstrates how to use the custom agent system, including:

- Creating agents
- Setting up an orchestrator
- Creating and executing workflows
- Direct agent interaction

## Running the Examples

To run the examples, use the following command:

```bash
python -m examples.custom_agent_example
```

## Creating Your Own Examples

Feel free to create your own examples to explore the agent system. Some ideas:

1. Create a specialized agent for a specific task
2. Implement a custom LLM provider
3. Create a more complex workflow with multiple agents
4. Implement a custom protocol transport

## Integration with the API

To see how the agent system is integrated with the Emma Studio API, check out:

- `backend/app/services/agent_service.py`: Service layer for the agent system
- `backend/app/api/endpoints/crew.py`: API endpoints that use the agent system
